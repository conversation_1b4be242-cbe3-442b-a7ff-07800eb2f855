from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from decimal import Decimal

class Customer(models.Model):
    """نموذج العملاء"""
    name = models.CharField(max_length=200, verbose_name="اسم العميل")
    email = models.EmailField(blank=True, null=True, verbose_name="البريد الإلكتروني")
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="رقم الهاتف")
    address = models.TextField(blank=True, null=True, verbose_name="العنوان")
    tax_number = models.CharField(max_length=50, blank=True, null=True, verbose_name="الرقم الضريبي")
    credit_limit = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="حد الائتمان")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "عميل"
        verbose_name_plural = "العملاء"
        ordering = ['name']

    def __str__(self):
        return self.name

class Product(models.Model):
    """نموذج المنتجات"""
    name = models.CharField(max_length=200, verbose_name="اسم المنتج")
    code = models.CharField(max_length=50, unique=True, verbose_name="كود المنتج")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر الوحدة")
    cost_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر التكلفة")
    stock_quantity = models.IntegerField(default=0, verbose_name="الكمية المتاحة")
    unit = models.CharField(max_length=20, default="قطعة", verbose_name="الوحدة")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "منتج"
        verbose_name_plural = "المنتجات"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

class SalesOrder(models.Model):
    """نموذج أوامر البيع"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('confirmed', 'مؤكد'),
        ('shipped', 'تم الشحن'),
        ('delivered', 'تم التسليم'),
        ('cancelled', 'ملغي'),
    ]

    order_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الطلب")
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, verbose_name="العميل")
    order_date = models.DateField(verbose_name="تاريخ الطلب")
    delivery_date = models.DateField(blank=True, null=True, verbose_name="تاريخ التسليم المتوقع")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="الحالة")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم")
    tax_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=15, verbose_name="نسبة الضريبة")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "أمر بيع"
        verbose_name_plural = "أوامر البيع"
        ordering = ['-created_at']

    def __str__(self):
        return f"أمر بيع {self.order_number} - {self.customer.name}"

    @property
    def subtotal(self):
        return sum(item.total_price for item in self.items.all())

    @property
    def discount_amount(self):
        return self.subtotal * (self.discount_percentage / 100)

    @property
    def tax_amount(self):
        return (self.subtotal - self.discount_amount) * (self.tax_percentage / 100)

    @property
    def total_amount(self):
        return self.subtotal - self.discount_amount + self.tax_amount

class SalesOrderItem(models.Model):
    """نموذج عناصر أمر البيع"""
    order = models.ForeignKey(SalesOrder, related_name='items', on_delete=models.CASCADE, verbose_name="أمر البيع")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0.01)], verbose_name="الكمية")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر الوحدة")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم")

    class Meta:
        verbose_name = "عنصر أمر بيع"
        verbose_name_plural = "عناصر أوامر البيع"

    def __str__(self):
        return f"{self.product.name} - {self.quantity} {self.product.unit}"

    @property
    def total_price(self):
        subtotal = self.quantity * self.unit_price
        discount = subtotal * (self.discount_percentage / 100)
        return subtotal - discount

class SalesInvoice(models.Model):
    """نموذج فواتير البيع"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('sent', 'مرسلة'),
        ('paid', 'مدفوعة'),
        ('overdue', 'متأخرة'),
        ('cancelled', 'ملغية'),
    ]

    invoice_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الفاتورة")
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, verbose_name="العميل")
    order = models.ForeignKey(SalesOrder, blank=True, null=True, on_delete=models.SET_NULL, verbose_name="أمر البيع")
    invoice_date = models.DateField(verbose_name="تاريخ الفاتورة")
    due_date = models.DateField(verbose_name="تاريخ الاستحقاق")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="الحالة")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم")
    tax_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=15, verbose_name="نسبة الضريبة")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "فاتورة بيع"
        verbose_name_plural = "فواتير البيع"
        ordering = ['-created_at']

    def __str__(self):
        return f"فاتورة {self.invoice_number} - {self.customer.name}"

    @property
    def subtotal(self):
        return sum(item.total_price for item in self.items.all())

    @property
    def discount_amount(self):
        return self.subtotal * (self.discount_percentage / 100)

    @property
    def tax_amount(self):
        return (self.subtotal - self.discount_amount) * (self.tax_percentage / 100)

    @property
    def total_amount(self):
        return self.subtotal - self.discount_amount + self.tax_amount

class SalesInvoiceItem(models.Model):
    """نموذج عناصر فاتورة البيع"""
    invoice = models.ForeignKey(SalesInvoice, related_name='items', on_delete=models.CASCADE, verbose_name="فاتورة البيع")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0.01)], verbose_name="الكمية")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر الوحدة")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم")

    class Meta:
        verbose_name = "عنصر فاتورة بيع"
        verbose_name_plural = "عناصر فواتير البيع"

    def __str__(self):
        return f"{self.product.name} - {self.quantity} {self.product.unit}"

    @property
    def total_price(self):
        subtotal = self.quantity * self.unit_price
        discount = subtotal * (self.discount_percentage / 100)
        return subtotal - discount
