from django import forms
from django.forms import inlineformset_factory
from .models import Customer, Product, SalesOrder, SalesOrderItem, SalesInvoice, SalesInvoiceItem

class CustomerForm(forms.ModelForm):
    """نموذج إضافة/تعديل العملاء"""
    class Meta:
        model = Customer
        fields = ['name', 'email', 'phone', 'address', 'tax_number', 'credit_limit', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم العميل'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'البريد الإلكتروني'}),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'العنوان'}),
            'tax_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الرقم الضريبي'}),
            'credit_limit': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class ProductForm(forms.ModelForm):
    """نموذج إضافة/تعديل المنتجات"""
    class Meta:
        model = Product
        fields = ['name', 'code', 'description', 'unit_price', 'cost_price', 'stock_quantity', 'unit', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم المنتج'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'كود المنتج'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف المنتج'}),
            'unit_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'cost_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'stock_quantity': forms.NumberInput(attrs={'class': 'form-control'}),
            'unit': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الوحدة'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class SalesOrderForm(forms.ModelForm):
    """نموذج إضافة/تعديل أوامر البيع"""
    class Meta:
        model = SalesOrder
        fields = ['customer', 'order_date', 'delivery_date', 'status', 'notes', 'discount_percentage', 'tax_percentage']
        widgets = {
            'customer': forms.Select(attrs={'class': 'form-select'}),
            'order_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'delivery_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
            'discount_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
            'tax_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
        }

class SalesOrderItemForm(forms.ModelForm):
    """نموذج عناصر أمر البيع"""
    class Meta:
        model = SalesOrderItem
        fields = ['product', 'quantity', 'unit_price', 'discount_percentage']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-select'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
            'unit_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'discount_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
        }

class SalesInvoiceForm(forms.ModelForm):
    """نموذج إضافة/تعديل فواتير البيع"""
    class Meta:
        model = SalesInvoice
        fields = ['customer', 'order', 'invoice_date', 'due_date', 'status', 'notes', 'discount_percentage', 'tax_percentage']
        widgets = {
            'customer': forms.Select(attrs={'class': 'form-select'}),
            'order': forms.Select(attrs={'class': 'form-select'}),
            'invoice_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'due_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
            'discount_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
            'tax_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
        }

class SalesInvoiceItemForm(forms.ModelForm):
    """نموذج عناصر فاتورة البيع"""
    class Meta:
        model = SalesInvoiceItem
        fields = ['product', 'quantity', 'unit_price', 'discount_percentage']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-select'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
            'unit_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'discount_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
        }

# إنشاء Formsets للعناصر
SalesOrderItemFormSet = inlineformset_factory(
    SalesOrder, 
    SalesOrderItem, 
    form=SalesOrderItemForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)

SalesInvoiceItemFormSet = inlineformset_factory(
    SalesInvoice, 
    SalesInvoiceItem, 
    form=SalesInvoiceItemForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)
