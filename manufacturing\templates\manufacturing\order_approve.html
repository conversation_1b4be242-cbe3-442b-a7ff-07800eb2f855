{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    
    .manufacturing-header {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        border-radius: 25px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        position: relative;
        overflow: hidden;
    }
    
    .manufacturing-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }
    
    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 900;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }
    
    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .section-title {
        color: #2c3e50;
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .section-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
    }
    
    .order-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin: 2rem 0;
    }
    
    .info-item {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid #667eea;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .info-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2c3e50;
    }
    
    .status-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .status-draft {
        background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
        color: #856404;
    }
    
    .raw-materials-table {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        margin: 2rem 0;
    }
    
    .raw-materials-table .table {
        margin: 0;
    }
    
    .raw-materials-table .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1.2rem;
        font-weight: 600;
        text-align: center;
    }
    
    .raw-materials-table .table tbody td {
        padding: 1rem;
        vertical-align: middle;
        text-align: center;
        border-bottom: 1px solid #e9ecef;
    }
    
    .raw-materials-table .table tbody tr:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin: 3rem 0;
        flex-wrap: wrap;
    }
    
    .btn-action {
        padding: 1rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1.1rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .btn-approve {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }
    
    .btn-approve:hover {
        background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
        transform: translateY(-2px);
        box-shadow: 0 12px 25px rgba(40, 167, 69, 0.3);
        color: white;
    }
    
    .btn-cancel {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
    }
    
    .btn-cancel:hover {
        background: linear-gradient(135deg, #5a6268 0%, #3d4449 100%);
        transform: translateY(-2px);
        box-shadow: 0 12px 25px rgba(108, 117, 125, 0.3);
        color: white;
    }
    
    .alert-custom {
        border-radius: 15px;
        border: none;
        padding: 1.5rem;
        margin: 1.5rem 0;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }
    
    .alert-warning-custom {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        color: #856404;
        border-left: 4px solid #ffc107;
    }
    
    .approval-warning {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border: 1px solid #2196f3;
        border-radius: 15px;
        padding: 1.5rem;
        margin: 2rem 0;
        color: #0d47a1;
    }
    
    .approval-warning h5 {
        color: #1565c0;
        font-weight: 700;
        margin-bottom: 1rem;
    }
    
    .approval-warning ul {
        margin: 0;
        padding-right: 1.5rem;
    }
    
    .approval-warning li {
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="manufacturing-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-check-circle me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'manufacturing:order_detail' order.id %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة لتفاصيل الأمر
                </a>
            </div>
        </div>
    </div>

    <!-- ملخص الأمر -->
    <div class="content-card">
        <h2 class="section-title">
            <div class="section-icon">
                <i class="bi bi-info-circle"></i>
            </div>
            ملخص أمر التصنيع
        </h2>
        
        <div class="order-info-grid">
            <div class="info-item">
                <div class="info-label">رقم الأمر</div>
                <div class="info-value">{{ order.order_number }}</div>
            </div>
            
            <div class="info-item">
                <div class="info-label">المنتج النهائي</div>
                <div class="info-value">{{ order.final_product.name }}</div>
            </div>
            
            <div class="info-item">
                <div class="info-label">الكمية المطلوبة</div>
                <div class="info-value">{{ order.quantity }} {{ order.unit_of_measure.name }}</div>
            </div>
            
            <div class="info-item">
                <div class="info-label">الحالة الحالية</div>
                <div class="info-value">
                    <span class="status-badge status-draft">
                        <i class="bi bi-clock me-1"></i>مسودة
                    </span>
                </div>
            </div>
            
            <div class="info-item">
                <div class="info-label">تاريخ الإنشاء</div>
                <div class="info-value">{{ order.created_at|date:"d/m/Y H:i" }}</div>
            </div>
            
            <div class="info-item">
                <div class="info-label">منشئ الأمر</div>
                <div class="info-value">{{ order.created_by.get_full_name|default:order.created_by.username }}</div>
            </div>
        </div>
    </div>

    <!-- المواد الخام المطلوبة -->
    <div class="content-card">
        <h2 class="section-title">
            <div class="section-icon">
                <i class="bi bi-box-seam"></i>
            </div>
            المواد الخام المطلوبة
        </h2>

        <div class="raw-materials-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>المادة الخام</th>
                        <th>الكمية المطلوبة</th>
                        <th>وحدة القياس</th>
                        <th>التكلفة المتوقعة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for raw_material in order.raw_materials.all %}
                    <tr>
                        <td>
                            <strong>{{ raw_material.raw_material.name }}</strong>
                            {% if raw_material.raw_material.code %}
                                <br><small class="text-muted">{{ raw_material.raw_material.code }}</small>
                            {% endif %}
                        </td>
                        <td>{{ raw_material.quantity }}</td>
                        <td>{{ raw_material.unit_of_measure.name }}</td>
                        <td>
                            {% if raw_material.estimated_cost %}
                                {{ raw_material.estimated_cost }} جنيه
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="4" class="text-center text-muted">لا توجد مواد خام مضافة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- تحذير الاعتماد -->
    <div class="approval-warning">
        <h5><i class="bi bi-exclamation-triangle me-2"></i>تنبيه مهم قبل الاعتماد</h5>
        <p>عند اعتماد أمر التصنيع، سيتم:</p>
        <ul>
            <li>تغيير حالة الأمر من "مسودة" إلى "معتمد"</li>
            <li>التحقق من توفر المواد الخام في المخزون</li>
            <li>منع تعديل تفاصيل الأمر بعد الاعتماد</li>
            <li>إتاحة إمكانية بدء الإنتاج</li>
        </ul>
        <p class="mb-0"><strong>تأكد من صحة جميع البيانات قبل المتابعة.</strong></p>
    </div>

    <!-- أزرار الإجراءات -->
    <form method="post">
        {% csrf_token %}
        <div class="action-buttons">
            <button type="submit" class="btn-action btn-approve"
                    onclick="return confirm('هل أنت متأكد من اعتماد أمر التصنيع؟ لن يمكن تعديله بعد الاعتماد.')">
                <i class="bi bi-check-circle-fill"></i>
                اعتماد الأمر
            </button>

            <a href="{% url 'manufacturing:order_detail' order.id %}" class="btn-action btn-cancel">
                <i class="bi bi-x-circle"></i>
                إلغاء
            </a>
        </div>
    </form>
</div>
{% endblock %}
