from functools import wraps
from django.shortcuts import redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.core.exceptions import PermissionDenied
from .models import UserProfile


def permission_required(module, permission_type, redirect_url='dashboard:index'):
    """
    ديكوريتر للتحقق من الصلاحيات
    
    Args:
        module: اسم الوحدة (مثل 'sales', 'purchases')
        permission_type: نوع الصلاحية (مثل 'view', 'add', 'edit', 'delete')
        redirect_url: الصفحة المراد التوجيه إليها في حالة عدم وجود صلاحية
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            try:
                # الحصول على ملف المستخدم
                user_profile = UserProfile.objects.get(user=request.user)
                
                # فحص الصلاحية
                if user_profile.has_permission(module, permission_type):
                    return view_func(request, *args, **kwargs)
                else:
                    # في حالة طلب AJAX
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return JsonResponse({
                            'success': False,
                            'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'
                        }, status=403)
                    
                    # في حالة الطلب العادي
                    messages.error(request, 'ليس لديك صلاحية للوصول إلى هذه الصفحة')
                    return redirect(redirect_url)
                    
            except UserProfile.DoesNotExist:
                # إنشاء ملف مستخدم جديد
                UserProfile.objects.create(user=request.user)
                messages.error(request, 'يرجى التواصل مع المدير لتعيين الصلاحيات')
                return redirect(redirect_url)
                
        return _wrapped_view
    return decorator


def superuser_required(redirect_url='dashboard:index'):
    """ديكوريتر للتحقق من صلاحيات المدير العام"""
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)
            else:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'success': False,
                        'message': 'هذه الصفحة مخصصة للمدير العام فقط'
                    }, status=403)
                
                messages.error(request, 'هذه الصفحة مخصصة للمدير العام فقط')
                return redirect(redirect_url)
                
        return _wrapped_view
    return decorator


def staff_required(redirect_url='dashboard:index'):
    """ديكوريتر للتحقق من صلاحيات الموظفين"""
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            if request.user.is_staff:
                return view_func(request, *args, **kwargs)
            else:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'success': False,
                        'message': 'هذه الصفحة مخصصة للموظفين فقط'
                    }, status=403)
                
                messages.error(request, 'هذه الصفحة مخصصة للموظفين فقط')
                return redirect(redirect_url)
                
        return _wrapped_view
    return decorator


class PermissionMixin:
    """
    Mixin للاستخدام مع Class-Based Views
    """
    permission_module = None
    permission_type = None
    redirect_url = 'dashboard:index'
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('accounts:login')
        
        if self.permission_module and self.permission_type:
            try:
                user_profile = UserProfile.objects.get(user=request.user)
                
                if not user_profile.has_permission(self.permission_module, self.permission_type):
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return JsonResponse({
                            'success': False,
                            'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'
                        }, status=403)
                    
                    messages.error(request, 'ليس لديك صلاحية للوصول إلى هذه الصفحة')
                    return redirect(self.redirect_url)
                    
            except UserProfile.DoesNotExist:
                UserProfile.objects.create(user=request.user)
                messages.error(request, 'يرجى التواصل مع المدير لتعيين الصلاحيات')
                return redirect(self.redirect_url)
        
        return super().dispatch(request, *args, **kwargs)


# دوال مساعدة للاستخدام في القوالب
def user_has_permission(user, module, permission_type):
    """فحص صلاحية المستخدم - للاستخدام في القوالب"""
    if user.is_superuser:
        return True
    
    try:
        user_profile = UserProfile.objects.get(user=user)
        return user_profile.has_permission(module, permission_type)
    except UserProfile.DoesNotExist:
        return False


def get_user_permissions(user):
    """الحصول على جميع صلاحيات المستخدم"""
    if user.is_superuser:
        return 'all'

    try:
        user_profile = UserProfile.objects.get(user=user)
        return user_profile.get_permissions_by_module()
    except UserProfile.DoesNotExist:
        return {}


# دالة لإنشاء الصلاحيات الافتراضية
def create_default_permissions():
    """إنشاء الصلاحيات الافتراضية للنظام"""
    from .models import Permission

    # قائمة الصلاحيات الافتراضية
    default_permissions = []

    modules = [
        ('dashboard', 'لوحة التحكم'),
        ('definitions', 'التعريفات'),
        ('warehouses', 'إدارة المخازن'),
        ('manufacturing', 'التصنيع'),
        ('sales', 'المبيعات'),
        ('purchases', 'المشتريات'),
        ('assets', 'الأصول الثابتة'),
        ('banks', 'البنوك'),
        ('treasuries', 'الخزائن'),
        ('accounting', 'الحسابات العامة'),
        ('branches', 'المركز الرئيسي والفروع'),
        ('hr', 'شؤون العاملين'),
        ('reports', 'التقارير'),
        ('settings', 'الإعدادات والخدمات'),
        ('users', 'إدارة المستخدمين'),
        ('system', 'إدارة النظام'),
    ]

    permission_types = [
        ('view', 'عرض'),
        ('add', 'إضافة'),
        ('edit', 'تعديل'),
        ('delete', 'حذف'),
        ('export', 'تصدير'),
        ('import', 'استيراد'),
        ('approve', 'موافقة'),
        ('manage', 'إدارة كاملة'),
    ]

    for module_code, module_name in modules:
        for perm_code, perm_name in permission_types:
            permission, created = Permission.objects.get_or_create(
                module=module_code,
                permission_type=perm_code,
                defaults={
                    'name': f'{perm_name} {module_name}',
                    'description': f'صلاحية {perm_name} في وحدة {module_name}',
                    'is_active': True
                }
            )
            if created:
                default_permissions.append(permission)

    return default_permissions
