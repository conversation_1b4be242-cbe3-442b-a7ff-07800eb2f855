{% extends 'permissions/base.html' %}

{% block title %}تعديل المنصب: {{ position.name }} - إدارة الصلاحيات{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'permissions:dashboard' %}">إدارة الصلاحيات</a></li>
        <li class="breadcrumb-item"><a href="{% url 'permissions:departments_positions' %}">الأقسام والمناصب</a></li>
        <li class="breadcrumb-item active">تعديل المنصب: {{ position.name }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="page-title">
                <i class="bi bi-pencil-square me-3"></i>
                تعديل المنصب: {{ position.name }}
            </h1>
            <p class="page-subtitle">تحديث معلومات المنصب الوظيفي</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'permissions:departments_positions' %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-right me-2"></i>
                العودة للأقسام والمناصب
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <i class="bi bi-pencil-square me-2"></i>
                تعديل بيانات المنصب
            </div>
            
            <div class="card-body">
                <form method="post" id="editPositionForm">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم المنصب *</label>
                                <input type="text" 
                                       class="form-control" 
                                       name="name" 
                                       value="{{ position.name }}"
                                       placeholder="أدخل اسم المنصب"
                                       required>
                                <div class="invalid-feedback">
                                    يجب أن يكون اسم المنصب على الأقل حرفين
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">القسم</label>
                                <select class="form-select" name="department">
                                    <option value="">-- اختر القسم --</option>
                                    {% for department in departments %}
                                    <option value="{{ department.id }}" 
                                            {% if position.department and position.department.id == department.id %}selected{% endif %}>
                                        {{ department.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">
                                    يمكن ترك هذا الحقل فارغاً إذا كان المنصب عام
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">وصف المنصب</label>
                        <textarea class="form-control" 
                                  name="description" 
                                  rows="4"
                                  placeholder="أدخل وصف تفصيلي للمنصب ومسؤولياته (اختياري)">{{ position.description }}</textarea>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   name="is_active" 
                                   id="positionActive"
                                   {% if position.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="positionActive">
                                <strong>المنصب نشط</strong>
                                <br><small class="text-muted">المنصب متاح للاستخدام وتعيين المستخدمين</small>
                            </label>
                        </div>
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-success btn-lg me-3">
                            <i class="bi bi-check-circle me-2"></i>
                            حفظ التغييرات
                        </button>
                        <a href="{% url 'permissions:departments_positions' %}" class="btn btn-outline-secondary btn-lg">
                            <i class="bi bi-x-circle me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="card mt-4">
            <div class="card-header">
                <i class="bi bi-info-circle me-2"></i>
                معلومات المنصب
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">تفاصيل المنصب</h6>
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>تاريخ الإنشاء:</strong></td>
                                <td>{{ position.created_at|date:"Y/m/d H:i" }}</td>
                            </tr>
                            <tr>
                                <td><strong>آخر تحديث:</strong></td>
                                <td>{{ position.updated_at|date:"Y/m/d H:i" }}</td>
                            </tr>
                            <tr>
                                <td><strong>القسم:</strong></td>
                                <td>{{ position.department.name|default:"غير محدد" }}</td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    {% if position.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="text-success">المستخدمين المرتبطين</h6>
                        {% if position.userprofile_set.exists %}
                            <div class="users-list">
                                {% for user_profile in position.userprofile_set.all|slice:":5" %}
                                <div class="user-item">
                                    <i class="bi bi-person me-2"></i>
                                    {{ user_profile.user.get_full_name|default:user_profile.user.username }}
                                </div>
                                {% endfor %}
                                
                                {% if position.userprofile_set.count > 5 %}
                                <div class="text-muted small">
                                    و {{ position.userprofile_set.count|add:"-5" }} مستخدم آخر...
                                </div>
                                {% endif %}
                            </div>
                        {% else %}
                            <div class="text-muted">
                                <i class="bi bi-info-circle me-2"></i>
                                لا يوجد مستخدمين مرتبطين بهذا المنصب
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                {% if position.userprofile_set.exists %}
                <div class="alert alert-warning mt-3">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>تنبيه:</strong> يوجد {{ position.userprofile_set.count }} مستخدم مرتبط بهذا المنصب. 
                    تأكد من أن التغييرات لن تؤثر على صلاحياتهم.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.form-check {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.form-check:hover {
    background: #e9ecef;
    transform: translateX(-2px);
}

.user-item {
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 0.5rem;
    border-left: 3px solid var(--success-color);
}

.users-list {
    max-height: 200px;
    overflow-y: auto;
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 2px solid var(--primary-color);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('editPositionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // تعطيل الزر وإظهار حالة التحميل
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
    
    fetch('{% url "permissions:edit_job_position" position.id %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إظهار رسالة نجاح
            showAlert('success', data.message);
            
            // إعادة توجيه بعد ثانيتين
            setTimeout(() => {
                window.location.href = '{% url "permissions:departments_positions" %}';
            }, 2000);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في الاتصال');
    })
    .finally(() => {
        // إعادة تفعيل الزر
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

function showAlert(type, message) {
    // إنشاء تنبيه مؤقت
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// التحقق من صحة النموذج
document.querySelector('input[name="name"]').addEventListener('input', function() {
    const value = this.value.trim();
    const submitBtn = document.querySelector('button[type="submit"]');
    
    if (value.length < 2) {
        this.classList.add('is-invalid');
        submitBtn.disabled = true;
    } else {
        this.classList.remove('is-invalid');
        submitBtn.disabled = false;
    }
});

// تأكيد قبل المغادرة إذا كان هناك تغييرات
let formChanged = false;
document.querySelectorAll('#editPositionForm input, #editPositionForm textarea, #editPositionForm select').forEach(input => {
    input.addEventListener('change', () => {
        formChanged = true;
    });
});

window.addEventListener('beforeunload', function(e) {
    if (formChanged) {
        e.preventDefault();
        e.returnValue = '';
    }
});
</script>
{% endblock %}
