from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q
from .models import Department, JobPosition, Permission, PermissionTemplate
from system_settings.models import UserProfile
from .decorators import permission_required, superuser_required
from .forms import UserProfileForm, PermissionTemplateForm


@login_required
@superuser_required()
def permissions_dashboard(request):
    """لوحة تحكم الصلاحيات"""
    context = {
        'total_users': User.objects.count(),
        'active_users': User.objects.filter(is_active=True).count(),
        'total_departments': Department.objects.count(),
        'total_positions': JobPosition.objects.count(),
        'total_permissions': Permission.objects.count(),
        'recent_users': User.objects.order_by('-date_joined')[:5],
    }
    return render(request, 'permissions/dashboard.html', context)


# تم نقل إدارة المستخدمين إلى system_settings
# استخدم system_settings:users_management بدلاً من permissions:user_list


@login_required
@superuser_required()
def user_permissions(request, user_id):
    """إدارة صلاحيات المستخدم"""
    # استخدام نموذج permissions.UserProfile للصلاحيات
    from .models import UserProfile as PermissionsUserProfile

    user = get_object_or_404(User, id=user_id)
    user_profile, created = PermissionsUserProfile.objects.get_or_create(user=user)
    
    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=user_profile)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث صلاحيات المستخدم {user.get_full_name() or user.username} بنجاح')
            return redirect('system_settings:users_management')
    else:
        form = UserProfileForm(instance=user_profile)
    
    # تجميع الصلاحيات حسب الوحدة
    permissions_by_module = {}
    for permission in Permission.objects.filter(is_active=True).order_by('module', 'permission_type'):
        if permission.module not in permissions_by_module:
            permissions_by_module[permission.module] = []
        permissions_by_module[permission.module].append(permission)
    
    context = {
        'user': user,
        'user_profile': user_profile,
        'form': form,
        'permissions_by_module': permissions_by_module,
    }
    return render(request, 'permissions/user_permissions.html', context)


@login_required
@superuser_required()
def permission_templates(request):
    """قوالب الصلاحيات"""
    templates = PermissionTemplate.objects.select_related('job_position').prefetch_related('permissions').all()

    # إضافة معلومات إضافية لكل قالب
    for template in templates:
        template.modules_count = template.permissions.values_list('module', flat=True).distinct().count()

    context = {
        'templates': templates,
    }
    return render(request, 'permissions/permission_templates.html', context)


@login_required
@superuser_required()
def create_permission_template(request):
    """إنشاء قالب صلاحيات جديد"""
    if request.method == 'POST':
        form = PermissionTemplateForm(request.POST)
        if form.is_valid():
            template = form.save()
            messages.success(request, f'تم إنشاء قالب الصلاحيات "{template.name}" بنجاح')
            return redirect('permissions:permission_templates')
    else:
        form = PermissionTemplateForm()
    
    # تجميع الصلاحيات حسب الوحدة
    permissions_by_module = {}
    for permission in Permission.objects.filter(is_active=True).order_by('module', 'permission_type'):
        if permission.module not in permissions_by_module:
            permissions_by_module[permission.module] = []
        permissions_by_module[permission.module].append(permission)
    
    context = {
        'form': form,
        'permissions_by_module': permissions_by_module,
    }
    return render(request, 'permissions/create_permission_template.html', context)


@login_required
@superuser_required()
def apply_template_to_user(request):
    """تطبيق قالب صلاحيات على مستخدم"""
    if request.method == 'POST':
        template_id = request.POST.get('template_id')
        user_id = request.POST.get('user_id')
        
        try:
            # استخدام نموذج permissions.UserProfile للصلاحيات
            from .models import UserProfile as PermissionsUserProfile

            template = PermissionTemplate.objects.get(id=template_id)
            user = User.objects.get(id=user_id)
            user_profile, created = PermissionsUserProfile.objects.get_or_create(user=user)
            
            template.apply_to_user(user_profile)
            
            return JsonResponse({
                'success': True,
                'message': f'تم تطبيق قالب "{template.name}" على المستخدم {user.get_full_name() or user.username} بنجاح'
            })
            
        except (PermissionTemplate.DoesNotExist, User.DoesNotExist):
            return JsonResponse({
                'success': False,
                'message': 'حدث خطأ في تطبيق القالب'
            })
    
    return JsonResponse({'success': False, 'message': 'طلب غير صحيح'})


@login_required
def template_details(request, template_id):
    """تفاصيل قالب الصلاحيات"""
    template = get_object_or_404(PermissionTemplate, id=template_id)

    # تجميع الصلاحيات حسب الوحدة
    permissions_by_module = {}
    for permission in template.permissions.all():
        module = permission.module
        if module not in permissions_by_module:
            permissions_by_module[module] = []
        permissions_by_module[module].append(permission)

    # إحصائيات القالب
    total_permissions = template.permissions.count()
    total_modules = len(permissions_by_module)

    # المستخدمين الذين يستخدمون هذا القالب (تقريبي)
    users_with_similar_permissions = UserProfile.objects.filter(
        permissions__in=template.permissions.all()
    ).distinct()[:10]  # أول 10 مستخدمين

    context = {
        'template': template,
        'permissions_by_module': permissions_by_module,
        'total_permissions': total_permissions,
        'total_modules': total_modules,
        'users_with_similar_permissions': users_with_similar_permissions,
    }

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # إذا كان الطلب AJAX، أرجع جزء من الصفحة فقط
        return render(request, 'permissions/template_details_modal.html', context)

    return render(request, 'permissions/template_details.html', context)


@login_required
@superuser_required()
def edit_permission_template(request, template_id):
    """تعديل قالب صلاحيات"""
    template = get_object_or_404(PermissionTemplate, id=template_id)

    if request.method == 'POST':
        form = PermissionTemplateForm(request.POST, instance=template)
        if form.is_valid():
            template = form.save()
            messages.success(request, f'تم تحديث القالب "{template.name}" بنجاح')
            return redirect('permissions:template_details', template_id=template.id)
    else:
        form = PermissionTemplateForm(instance=template)

    # تجميع الصلاحيات حسب الوحدة
    permissions_by_module = {}
    for permission in Permission.objects.filter(is_active=True).order_by('module', 'permission_type'):
        if permission.module not in permissions_by_module:
            permissions_by_module[permission.module] = []
        permissions_by_module[permission.module].append(permission)

    # الصلاحيات المحددة حالياً
    selected_permissions = list(template.permissions.values_list('id', flat=True))

    context = {
        'form': form,
        'template': template,
        'permissions_by_module': permissions_by_module,
        'selected_permissions': selected_permissions,
    }
    return render(request, 'permissions/edit_permission_template.html', context)


@login_required
@superuser_required()
def departments_positions(request):
    """إدارة الأقسام والمناصب"""
    departments = Department.objects.prefetch_related('jobposition_set').all()
    
    context = {
        'departments': departments,
    }
    return render(request, 'permissions/departments_positions.html', context)


# تم نقل إضافة المستخدمين إلى system_settings
# استخدم system_settings:user_create بدلاً من permissions:add_user


@login_required
def my_permissions(request):
    """صلاحياتي"""
    # استخدام نموذج permissions.UserProfile للصلاحيات
    from .models import UserProfile as PermissionsUserProfile

    try:
        user_profile = PermissionsUserProfile.objects.get(user=request.user)
        permissions_by_module = user_profile.get_permissions_by_module()
    except PermissionsUserProfile.DoesNotExist:
        # إنشاء ملف مستخدم فارغ إذا لم يكن موجوداً
        user_profile = PermissionsUserProfile.objects.create(user=request.user)
        permissions_by_module = {}

    context = {
        'user_profile': user_profile,
        'permissions_by_module': permissions_by_module,
    }
    return render(request, 'permissions/my_permissions.html', context)


@login_required
@superuser_required()
def edit_job_position(request, position_id):
    """تعديل منصب وظيفي"""
    position = get_object_or_404(JobPosition, id=position_id)

    if request.method == 'POST':
        name = request.POST.get('name', '').strip()
        description = request.POST.get('description', '').strip()
        department_id = request.POST.get('department')
        is_active = request.POST.get('is_active') == 'on'

        if not name:
            return JsonResponse({
                'success': False,
                'message': 'اسم المنصب مطلوب'
            })

        # التحقق من عدم تكرار الاسم (باستثناء المنصب الحالي)
        if JobPosition.objects.filter(name=name, department_id=department_id).exclude(id=position_id).exists():
            return JsonResponse({
                'success': False,
                'message': 'يوجد منصب بنفس الاسم في هذا القسم'
            })

        try:
            department = Department.objects.get(id=department_id) if department_id else None

            position.name = name
            position.description = description
            position.department = department
            position.is_active = is_active
            position.save()

            return JsonResponse({
                'success': True,
                'message': f'تم تحديث المنصب "{position.name}" بنجاح',
                'position': {
                    'id': position.id,
                    'name': position.name,
                    'description': position.description,
                    'department': position.department.name if position.department else 'غير محدد',
                    'is_active': position.is_active
                }
            })

        except Department.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'القسم المحدد غير موجود'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'حدث خطأ: {str(e)}'
            })

    # GET request - إرجاع بيانات المنصب
    departments = Department.objects.filter(is_active=True).order_by('name')

    context = {
        'position': position,
        'departments': departments,
    }

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return render(request, 'permissions/edit_position_modal.html', context)

    return render(request, 'permissions/edit_position.html', context)
