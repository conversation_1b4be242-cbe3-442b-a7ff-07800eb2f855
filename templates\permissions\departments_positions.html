{% extends 'permissions/base.html' %}

{% block title %}الأقسام والمناصب - إدارة الصلاحيات{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'permissions:dashboard' %}">إدارة الصلاحيات</a></li>
        <li class="breadcrumb-item active">الأقسام والمناصب</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="page-title">
                <i class="bi bi-building me-3"></i>
                إدارة الأقسام والمناصب
            </h1>
            <p class="page-subtitle">إدارة الهيكل التنظيمي للشركة والمناصب الوظيفية</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group">
                <button class="btn btn-success" onclick="showAddDepartmentModal()">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة قسم
                </button>
                <button class="btn btn-primary" onclick="showAddPositionModal()">
                    <i class="bi bi-briefcase-fill me-2"></i>
                    إضافة منصب
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Departments and Positions -->
<div class="row">
    {% for department in departments %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card department-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0">{{ department.name }}</h5>
                    <small class="text-light">
                        <i class="bi bi-people me-1"></i>
                        {{ department.jobposition_set.count }} منصب
                    </small>
                </div>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-three-dots-vertical"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="#" onclick="editDepartment({{ department.id }})">
                                <i class="bi bi-pencil me-2"></i>تعديل القسم
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" onclick="addPositionToDepartment({{ department.id }})">
                                <i class="bi bi-plus me-2"></i>إضافة منصب
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="#" onclick="deleteDepartment({{ department.id }})">
                                <i class="bi bi-trash me-2"></i>حذف القسم
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="card-body">
                {% if department.description %}
                    <p class="text-muted small mb-3">{{ department.description }}</p>
                {% endif %}
                
                <div class="positions-list">
                    <h6 class="text-muted mb-3">
                        <i class="bi bi-briefcase me-2"></i>
                        المناصب الوظيفية
                    </h6>
                    
                    {% if department.jobposition_set.all %}
                        <div class="list-group list-group-flush">
                            {% for position in department.jobposition_set.all %}
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <div>
                                    <strong>{{ position.name }}</strong>
                                    {% if position.description %}
                                        <br><small class="text-muted">{{ position.description }}</small>
                                    {% endif %}
                                </div>
                                <div class="btn-group btn-group-sm">
                                    {% if position.is_active %}
                                        <span class="badge badge-success">نشط</span>
                                    {% else %}
                                        <span class="badge badge-danger">غير نشط</span>
                                    {% endif %}
                                    <button class="btn btn-outline-primary btn-sm" onclick="editPosition({{ position.id }})">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="deletePosition({{ position.id }})">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="bi bi-briefcase text-muted"></i>
                            <p class="text-muted small mb-0">لا توجد مناصب في هذا القسم</p>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        {% if department.is_active %}
                            <i class="bi bi-check-circle text-success me-1"></i>نشط
                        {% else %}
                            <i class="bi bi-x-circle text-danger me-1"></i>غير نشط
                        {% endif %}
                    </small>
                    <button class="btn btn-outline-primary btn-sm" onclick="addPositionToDepartment({{ department.id }})">
                        <i class="bi bi-plus me-1"></i>إضافة منصب
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="bi bi-building fs-1 text-muted"></i>
            <h5 class="text-muted mt-3">لا توجد أقسام</h5>
            <p class="text-muted">ابدأ بإنشاء قسم جديد</p>
            <button class="btn btn-primary" onclick="showAddDepartmentModal()">
                <i class="bi bi-plus-circle me-2"></i>
                إضافة قسم جديد
            </button>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Add Department Modal -->
<div class="modal fade" id="addDepartmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة قسم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addDepartmentForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم القسم *</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">وصف القسم</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="is_active" checked>
                        <label class="form-check-label">القسم نشط</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check me-2"></i>إضافة القسم
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Position Modal -->
<div class="modal fade" id="addPositionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة منصب جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addPositionForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">القسم *</label>
                        <select class="form-select" name="department" required>
                            <option value="">-- اختر القسم --</option>
                            {% for dept in departments %}
                                <option value="{{ dept.id }}">{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">اسم المنصب *</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">وصف المنصب</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="is_active" checked>
                        <label class="form-check-label">المنصب نشط</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check me-2"></i>إضافة المنصب
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .department-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    
    .department-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .department-card .card-header {
        background: linear-gradient(135deg, var(--info-color), var(--primary-color));
        color: white;
        border: none;
    }
    
    .positions-list {
        max-height: 300px;
        overflow-y: auto;
    }
    
    .list-group-item {
        border: none;
        border-bottom: 1px solid #dee2e6;
        padding: 0.75rem 0;
    }
    
    .list-group-item:last-child {
        border-bottom: none;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
function showAddDepartmentModal() {
    new bootstrap.Modal(document.getElementById('addDepartmentModal')).show();
}

function showAddPositionModal() {
    new bootstrap.Modal(document.getElementById('addPositionModal')).show();
}

function addPositionToDepartment(departmentId) {
    const select = document.querySelector('#addPositionModal select[name="department"]');
    select.value = departmentId;
    showAddPositionModal();
}

// Handle form submissions
document.getElementById('addDepartmentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('سيتم إضافة وظيفة إنشاء الأقسام قريباً');
});

document.getElementById('addPositionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('سيتم إضافة وظيفة إنشاء المناصب قريباً');
});

function editDepartment(departmentId) {
    alert('سيتم إضافة وظيفة تعديل القسم قريباً');
}

function deleteDepartment(departmentId) {
    if (confirm('هل أنت متأكد من حذف هذا القسم؟ سيتم حذف جميع المناصب المرتبطة به.')) {
        alert('سيتم إضافة وظيفة حذف القسم قريباً');
    }
}

function editPosition(positionId) {
    // تحميل نموذج تعديل المنصب
    fetch(`/permissions/positions/${positionId}/edit/`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        // إنشاء Modal إذا لم يكن موجوداً
        let modal = document.getElementById('editPositionModal');
        if (!modal) {
            modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'editPositionModal';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content" id="editPositionContent">
                        ${html}
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        } else {
            document.getElementById('editPositionContent').innerHTML = html;
        }

        // إظهار Modal
        new bootstrap.Modal(modal).show();
    })
    .catch(error => {
        console.error('Error loading edit form:', error);
        alert('حدث خطأ في تحميل نموذج التعديل');
    });
}

function deletePosition(positionId) {
    if (confirm('هل أنت متأكد من حذف هذا المنصب؟')) {
        alert('سيتم إضافة وظيفة حذف المنصب قريباً');
    }
}
</script>
{% endblock %}
