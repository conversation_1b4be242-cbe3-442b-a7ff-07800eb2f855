{% extends 'permissions/base.html' %}

{% block title %}تعديل القالب: {{ template.name }} - إدارة الصلاحيات{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'permissions:dashboard' %}">إدارة الصلاحيات</a></li>
        <li class="breadcrumb-item"><a href="{% url 'permissions:permission_templates' %}">قوالب الصلاحيات</a></li>
        <li class="breadcrumb-item"><a href="{% url 'permissions:template_details' template.id %}">{{ template.name }}</a></li>
        <li class="breadcrumb-item active">تعديل</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="page-title">
                <i class="bi bi-pencil-square me-3"></i>
                تعديل القالب: {{ template.name }}
            </h1>
            <p class="page-subtitle">تحديث معلومات وصلاحيات القالب</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'permissions:template_details' template.id %}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-right me-2"></i>
                العودة للتفاصيل
            </a>
            <a href="{% url 'permissions:permission_templates' %}" class="btn btn-outline-primary">
                <i class="bi bi-list me-2"></i>
                كل القوالب
            </a>
        </div>
    </div>
</div>

<form method="post" id="editTemplateForm">
    {% csrf_token %}
    
    <div class="row">
        <!-- معلومات القالب -->
        <div class="col-lg-4">
            <div class="card sticky-top" style="top: 2rem;">
                <div class="card-header">
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات القالب
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">{{ form.name.label }} *</label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="text-danger small">{{ form.name.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ form.description.label }}</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger small">{{ form.description.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                {{ form.is_active.label }}
                            </label>
                        </div>
                        {% if form.is_active.errors %}
                            <div class="text-danger small">{{ form.is_active.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- إحصائيات سريعة -->
                    <hr>
                    <h6 class="text-muted mb-3">إحصائيات القالب</h6>
                    <div class="stats-mini">
                        <div class="stat-mini">
                            <span class="stat-number" id="selectedCount">{{ selected_permissions|length }}</span>
                            <span class="stat-label">صلاحية محددة</span>
                        </div>
                        <div class="stat-mini">
                            <span class="stat-number">{{ permissions_by_module|length }}</span>
                            <span class="stat-label">وحدة متاحة</span>
                        </div>
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <hr>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-check-circle me-2"></i>
                            حفظ التغييرات
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="selectAllPermissions()">
                            <i class="bi bi-check-all me-2"></i>
                            تحديد الكل
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearAllPermissions()">
                            <i class="bi bi-x-circle me-2"></i>
                            إلغاء التحديد
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الصلاحيات -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <i class="bi bi-shield-check me-2"></i>
                    اختيار الصلاحيات
                    <span class="badge bg-primary ms-2" id="selectedBadge">{{ selected_permissions|length }} محدد</span>
                </div>
                <div class="card-body">
                    {% if permissions_by_module %}
                        <div class="accordion" id="permissionsAccordion">
                            {% for module, permissions in permissions_by_module.items %}
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" 
                                            data-bs-toggle="collapse" 
                                            data-bs-target="#collapse{{ forloop.counter }}"
                                            aria-expanded="false">
                                        <div class="d-flex align-items-center justify-content-between w-100 me-3">
                                            <div>
                                                <i class="bi bi-folder me-2"></i>
                                                {{ module }}
                                            </div>
                                            <div>
                                                <span class="badge bg-secondary me-2">{{ permissions|length }}</span>
                                                <span class="badge bg-success module-selected-count" data-module="{{ forloop.counter }}">0</span>
                                            </div>
                                        </div>
                                    </button>
                                </h2>
                                <div id="collapse{{ forloop.counter }}" 
                                     class="accordion-collapse collapse" 
                                     data-bs-parent="#permissionsAccordion">
                                    <div class="accordion-body">
                                        <div class="module-actions mb-3">
                                            <button type="button" class="btn btn-sm btn-outline-success me-2" 
                                                    onclick="selectModulePermissions('{{ forloop.counter }}')">
                                                <i class="bi bi-check-all me-1"></i>
                                                تحديد الكل
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" 
                                                    onclick="clearModulePermissions('{{ forloop.counter }}')">
                                                <i class="bi bi-x-circle me-1"></i>
                                                إلغاء التحديد
                                            </button>
                                        </div>
                                        
                                        <div class="row">
                                            {% for permission in permissions %}
                                            <div class="col-md-6 mb-2">
                                                <div class="form-check permission-item">
                                                    <input class="form-check-input permission-checkbox" 
                                                           type="checkbox" 
                                                           name="permissions" 
                                                           value="{{ permission.id }}"
                                                           id="permission_{{ permission.id }}"
                                                           data-module="{{ forloop.parentloop.counter }}"
                                                           {% if permission.id in selected_permissions %}checked{% endif %}>
                                                    <label class="form-check-label" for="permission_{{ permission.id }}">
                                                        <strong>{{ permission.get_permission_type_display }}</strong>
                                                        {% if permission.description %}
                                                            <br><small class="text-muted">{{ permission.description }}</small>
                                                        {% endif %}
                                                    </label>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            لا توجد صلاحيات متاحة في النظام.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</form>

<!-- معاينة التغييرات -->
<div class="card mt-4">
    <div class="card-header">
        <i class="bi bi-eye me-2"></i>
        معاينة التغييرات
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-success">الصلاحيات المضافة</h6>
                <div id="addedPermissions" class="permissions-preview">
                    <small class="text-muted">لا توجد صلاحيات مضافة</small>
                </div>
            </div>
            <div class="col-md-6">
                <h6 class="text-danger">الصلاحيات المحذوفة</h6>
                <div id="removedPermissions" class="permissions-preview">
                    <small class="text-muted">لا توجد صلاحيات محذوفة</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.stats-mini {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
}

.stat-mini {
    text-align: center;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 6px;
    flex: 1;
}

.stat-number {
    display: block;
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
}

.permission-item {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 0.75rem;
    border-left: 3px solid #dee2e6;
    transition: all 0.3s ease;
}

.permission-item:has(.form-check-input:checked) {
    background: #e8f5e8;
    border-left-color: var(--success-color);
}

.permission-item:hover {
    background: #e9ecef;
}

.module-actions {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 1rem;
}

.accordion-button {
    background: #f8f9fa;
}

.accordion-button:not(.collapsed) {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    color: var(--primary-color);
}

.permissions-preview {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 1rem;
    background: #f8f9fa;
}

.permission-tag {
    display: inline-block;
    background: #e9ecef;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    margin: 0.2rem;
}

.permission-tag.added {
    background: #d4edda;
    color: #155724;
}

.permission-tag.removed {
    background: #f8d7da;
    color: #721c24;
}

.sticky-top {
    position: sticky;
    z-index: 1020;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// الصلاحيات المحددة في البداية
const originalPermissions = {{ selected_permissions|safe }};
let currentPermissions = [...originalPermissions];

// تحديث العدادات
function updateCounts() {
    const selectedCheckboxes = document.querySelectorAll('.permission-checkbox:checked');
    const selectedCount = selectedCheckboxes.length;
    
    // تحديث العداد الرئيسي
    document.getElementById('selectedCount').textContent = selectedCount;
    document.getElementById('selectedBadge').textContent = selectedCount + ' محدد';
    
    // تحديث عدادات الوحدات
    document.querySelectorAll('.module-selected-count').forEach(badge => {
        const moduleNum = badge.dataset.module;
        const moduleCheckboxes = document.querySelectorAll(`input[data-module="${moduleNum}"]:checked`);
        badge.textContent = moduleCheckboxes.length;
    });
    
    // تحديث معاينة التغييرات
    updatePreview();
}

// تحديث معاينة التغييرات
function updatePreview() {
    const currentSelected = Array.from(document.querySelectorAll('.permission-checkbox:checked'))
        .map(cb => parseInt(cb.value));
    
    const added = currentSelected.filter(id => !originalPermissions.includes(id));
    const removed = originalPermissions.filter(id => !currentSelected.includes(id));
    
    // عرض الصلاحيات المضافة
    const addedContainer = document.getElementById('addedPermissions');
    if (added.length > 0) {
        addedContainer.innerHTML = added.map(id => {
            const checkbox = document.querySelector(`input[value="${id}"]`);
            const label = checkbox.nextElementSibling.querySelector('strong').textContent;
            return `<span class="permission-tag added">${label}</span>`;
        }).join('');
    } else {
        addedContainer.innerHTML = '<small class="text-muted">لا توجد صلاحيات مضافة</small>';
    }
    
    // عرض الصلاحيات المحذوفة
    const removedContainer = document.getElementById('removedPermissions');
    if (removed.length > 0) {
        removedContainer.innerHTML = removed.map(id => {
            const checkbox = document.querySelector(`input[value="${id}"]`);
            const label = checkbox.nextElementSibling.querySelector('strong').textContent;
            return `<span class="permission-tag removed">${label}</span>`;
        }).join('');
    } else {
        removedContainer.innerHTML = '<small class="text-muted">لا توجد صلاحيات محذوفة</small>';
    }
}

// تحديد جميع الصلاحيات
function selectAllPermissions() {
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
    updateCounts();
}

// إلغاء تحديد جميع الصلاحيات
function clearAllPermissions() {
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    updateCounts();
}

// تحديد صلاحيات وحدة معينة
function selectModulePermissions(moduleNum) {
    document.querySelectorAll(`input[data-module="${moduleNum}"]`).forEach(checkbox => {
        checkbox.checked = true;
    });
    updateCounts();
}

// إلغاء تحديد صلاحيات وحدة معينة
function clearModulePermissions(moduleNum) {
    document.querySelectorAll(`input[data-module="${moduleNum}"]`).forEach(checkbox => {
        checkbox.checked = false;
    });
    updateCounts();
}

// مراقبة تغييرات الصلاحيات
document.addEventListener('DOMContentLoaded', function() {
    // تحديث العدادات في البداية
    updateCounts();
    
    // مراقبة تغييرات الصلاحيات
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateCounts);
    });
    
    // تأكيد قبل المغادرة إذا كان هناك تغييرات
    window.addEventListener('beforeunload', function(e) {
        const currentSelected = Array.from(document.querySelectorAll('.permission-checkbox:checked'))
            .map(cb => parseInt(cb.value));
        
        const hasChanges = currentSelected.length !== originalPermissions.length ||
                          !currentSelected.every(id => originalPermissions.includes(id));
        
        if (hasChanges) {
            e.preventDefault();
            e.returnValue = '';
        }
    });
});

// تأكيد الحفظ
document.getElementById('editTemplateForm').addEventListener('submit', function(e) {
    const selectedCount = document.querySelectorAll('.permission-checkbox:checked').length;
    
    if (selectedCount === 0) {
        e.preventDefault();
        alert('يجب تحديد صلاحية واحدة على الأقل');
        return;
    }
    
    if (!confirm(`هل أنت متأكد من حفظ التغييرات؟ تم تحديد ${selectedCount} صلاحية.`)) {
        e.preventDefault();
    }
});
</script>
{% endblock %}
