{% extends 'permissions/base.html' %}

{% block title %}{% if user_language == 'en' %}Permissions Dashboard - Osaric System{% else %}لوحة تحكم الصلاحيات - نظام أوساريك{% endif %}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">{% if user_language == 'en' %}Home{% else %}الرئيسية{% endif %}</a></li>
        <li class="breadcrumb-item active">{% if user_language == 'en' %}Permissions Management{% else %}إدارة الصلاحيات{% endif %}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<!-- تنبيه النظام الجديد -->
<div class="alert alert-info alert-dismissible fade show mb-4" role="alert">
    <i class="bi bi-info-circle me-2"></i>
    <strong>{% if user_language == 'en' %}Important Update:{% else %}تحديث مهم:{% endif %}</strong> {% if user_language == 'en' %}User management has been moved to the new system in{% else %}تم نقل إدارة المستخدمين إلى النظام الجديد في{% endif %}
    <a href="{% url 'system_settings:users_management' %}" class="alert-link">{% if user_language == 'en' %}System Settings{% else %}إعدادات النظام{% endif %}</a>
    {% if user_language == 'en' %}for better experience and advanced features.{% else %}للحصول على تجربة أفضل وميزات متطورة.{% endif %}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>

<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="page-title">
                <i class="bi bi-speedometer2 me-3"></i>
                {% if user_language == 'en' %}Permissions Dashboard{% else %}لوحة تحكم الصلاحيات{% endif %}
            </h1>
            <p class="page-subtitle">{% if user_language == 'en' %}Comprehensive management of user permissions and departments in Osaric system{% else %}إدارة شاملة لصلاحيات المستخدمين والأقسام في نظام أوساريك{% endif %}</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="d-flex gap-2 justify-content-end">
                <a href="{% url 'system_settings:users_management' %}" class="btn btn-primary">
                    <i class="bi bi-people me-2"></i>
                    {% if user_language == 'en' %}User Management{% else %}إدارة المستخدمين{% endif %}
                    <small class="d-block">{% if user_language == 'en' %}New System{% else %}النظام الجديد{% endif %}</small>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-number">{{ total_users }}</div>
            <div class="stats-label">
                <i class="bi bi-people me-2"></i>
                {% if user_language == 'en' %}Total Users{% else %}إجمالي المستخدمين{% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="border-left-color: var(--success-color);">
            <div class="stats-number" style="color: var(--success-color);">{{ active_users }}</div>
            <div class="stats-label">
                <i class="bi bi-person-check me-2"></i>
                {% if user_language == 'en' %}Active Users{% else %}المستخدمين النشطين{% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="border-left-color: var(--info-color);">
            <div class="stats-number" style="color: var(--info-color);">{{ total_departments }}</div>
            <div class="stats-label">
                <i class="bi bi-building me-2"></i>
                {% if user_language == 'en' %}Departments{% else %}الأقسام{% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card" style="border-left-color: var(--warning-color);">
            <div class="stats-number" style="color: var(--warning-color);">{{ total_positions }}</div>
            <div class="stats-label">
                <i class="bi bi-briefcase me-2"></i>
                {% if user_language == 'en' %}Job Positions{% else %}المناصب الوظيفية{% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="bi bi-lightning me-2"></i>
                {% if user_language == 'en' %}Quick Actions{% else %}إجراءات سريعة{% endif %}
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'system_settings:users_management' %}" class="btn btn-outline-primary w-100 py-3">
                            <i class="bi bi-people fs-4 d-block mb-2"></i>
                            إدارة المستخدمين
                            <small class="d-block text-muted mt-1">النظام الجديد</small>
                        </a>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'permissions:permission_templates' %}" class="btn btn-outline-success w-100 py-3">
                            <i class="bi bi-file-earmark-text fs-4 d-block mb-2"></i>
                            قوالب الصلاحيات
                        </a>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'permissions:departments_positions' %}" class="btn btn-outline-info w-100 py-3">
                            <i class="bi bi-building fs-4 d-block mb-2"></i>
                            الأقسام والمناصب
                        </a>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'permissions:create_permission_template' %}" class="btn btn-outline-warning w-100 py-3">
                            <i class="bi bi-plus-circle fs-4 d-block mb-2"></i>
                            إنشاء قالب جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Users -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <i class="bi bi-clock-history me-2"></i>
                المستخدمين الجدد
            </div>
            <div class="card-body">
                {% if recent_users %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in recent_users %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-3">
                                                <i class="bi bi-person"></i>
                                            </div>
                                            <div>
                                                <strong>{{ user.get_full_name|default:user.username }}</strong>
                                                <br>
                                                <small class="text-muted">{{ user.username }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ user.email|default:"غير محدد" }}</td>
                                    <td>{{ user.date_joined|date:"Y-m-d H:i" }}</td>
                                    <td>
                                        {% if user.is_active %}
                                            <span class="badge badge-success">نشط</span>
                                        {% else %}
                                            <span class="badge badge-danger">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'permissions:user_permissions' user.id %}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-gear"></i>
                                            إدارة الصلاحيات
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-people fs-1 text-muted"></i>
                        <p class="text-muted mt-3">لا توجد مستخدمين جدد</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <i class="bi bi-info-circle me-2"></i>
                معلومات النظام
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>إجمالي الصلاحيات:</strong>
                    <span class="float-end badge bg-primary">{{ total_permissions }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>الوحدات المتاحة:</strong>
                    <span class="float-end badge bg-info">16</span>
                </div>
                
                <div class="mb-3">
                    <strong>أنواع الصلاحيات:</strong>
                    <span class="float-end badge bg-success">8</span>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <h6 class="text-muted">نظام أوساريك للصلاحيات</h6>
                    <p class="small text-muted">الإصدار 2.0 - نظام متطور وآمن</p>
                </div>
            </div>
        </div>
        
        <!-- Quick Tips -->
        <div class="card mt-3">
            <div class="card-header">
                <i class="bi bi-lightbulb me-2"></i>
                نصائح سريعة
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <i class="bi bi-check-circle text-success me-2"></i>
                    <small>استخدم قوالب الصلاحيات لتوفير الوقت</small>
                </div>
                
                <div class="mb-3">
                    <i class="bi bi-check-circle text-success me-2"></i>
                    <small>راجع صلاحيات المستخدمين بانتظام</small>
                </div>
                
                <div class="mb-3">
                    <i class="bi bi-check-circle text-success me-2"></i>
                    <small>احرص على تحديث المناصب الوظيفية</small>
                </div>
                
                <div>
                    <i class="bi bi-check-circle text-success me-2"></i>
                    <small>استخدم نظام الأقسام لتنظيم أفضل</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar-circle {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
    }
</style>
{% endblock %}
