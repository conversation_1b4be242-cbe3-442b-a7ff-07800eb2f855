{% extends 'permissions/base.html' %}

{% block title %}إدارة صلاحيات {{ user.get_full_name|default:user.username }} - نظام أوساريك{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'permissions:dashboard' %}">إدارة الصلاحيات</a></li>
        <li class="breadcrumb-item"><a href="{% url 'system_settings:users_management' %}">المستخدمين</a></li>
        <li class="breadcrumb-item active">{{ user.get_full_name|default:user.username }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <div class="d-flex align-items-center">
                <div class="user-avatar me-4">
                    {% if user_profile.avatar %}
                        <img src="{{ user_profile.avatar.url }}" alt="Avatar" class="rounded-circle" width="80" height="80">
                    {% else %}
                        <div class="avatar-circle-large">
                            <i class="bi bi-person"></i>
                        </div>
                    {% endif %}
                </div>
                <div>
                    <h1 class="page-title mb-2">{{ user.get_full_name|default:user.username }}</h1>
                    <p class="page-subtitle mb-1">
                        <i class="bi bi-at me-2"></i>{{ user.username }}
                        {% if user.email %}
                            <span class="mx-2">•</span>
                            <i class="bi bi-envelope me-2"></i>{{ user.email }}
                        {% endif %}
                    </p>
                    <div class="d-flex gap-2 mt-2">
                        {% if user.is_active %}
                            <span class="badge badge-success">
                                <i class="bi bi-check-circle me-1"></i>نشط
                            </span>
                        {% else %}
                            <span class="badge badge-danger">
                                <i class="bi bi-x-circle me-1"></i>غير نشط
                            </span>
                        {% endif %}
                        
                        {% if user.is_superuser %}
                            <span class="badge bg-warning text-dark">
                                <i class="bi bi-star me-1"></i>مدير عام
                            </span>
                        {% endif %}
                        
                        {% if user_profile.job_position %}
                            <span class="badge bg-info">
                                <i class="bi bi-briefcase me-1"></i>{{ user_profile.job_position.name }}
                            </span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'system_settings:users_management' %}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-right me-2"></i>
                العودة للقائمة
            </a>
            <button class="btn btn-primary" onclick="editUserInfo({{ user.id }})">
                <i class="bi bi-pencil me-2"></i>
                تعديل المستخدم
            </button>
        </div>
    </div>
</div>

<div class="row">
    <!-- User Info -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <i class="bi bi-info-circle me-2"></i>
                معلومات المستخدم
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label class="form-label">المنصب الوظيفي</label>
                        {{ form.job_position }}
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">رقم الهاتف</label>
                        {{ form.phone }}
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الصورة الشخصية</label>
                        {{ form.avatar }}
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                المستخدم نشط
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-check me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Quick Templates -->
        <div class="card">
            <div class="card-header">
                <i class="bi bi-lightning me-2"></i>
                قوالب سريعة
            </div>
            <div class="card-body">
                <p class="text-muted small mb-3">تطبيق قالب صلاحيات جاهز على هذا المستخدم</p>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="applyTemplate(1)">
                        <i class="bi bi-star me-2"></i>مدير عام
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="applyTemplate(2)">
                        <i class="bi bi-cart me-2"></i>مدير مبيعات
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="applyTemplate(3)">
                        <i class="bi bi-bag me-2"></i>مدير مشتريات
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="applyTemplate(4)">
                        <i class="bi bi-calculator me-2"></i>محاسب
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Permissions -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>
                    <i class="bi bi-shield-check me-2"></i>
                    إدارة الصلاحيات
                </span>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="selectAllPermissions()">
                        <i class="bi bi-check-all me-1"></i>تحديد الكل
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearAllPermissions()">
                        <i class="bi bi-x-square me-1"></i>إلغاء الكل
                    </button>
                </div>
            </div>
            
            <div class="card-body">
                <form method="post" id="permissionsForm">
                    {% csrf_token %}
                    
                    <!-- Permissions by Module -->
                    <div class="accordion" id="permissionsAccordion">
                        {% for module, permissions in permissions_by_module.items %}
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading{{ forloop.counter }}">
                                <button class="accordion-button {% if not forloop.first %}collapsed{% endif %}" 
                                        type="button" 
                                        data-bs-toggle="collapse" 
                                        data-bs-target="#collapse{{ forloop.counter }}"
                                        aria-expanded="{% if forloop.first %}true{% else %}false{% endif %}"
                                        aria-controls="collapse{{ forloop.counter }}">
                                    <i class="bi bi-folder me-2"></i>
                                    <strong>{{ permissions.0.get_module_display }}</strong>
                                    <span class="badge bg-primary ms-auto me-3">{{ permissions|length }} صلاحية</span>
                                </button>
                            </h2>
                            <div id="collapse{{ forloop.counter }}" 
                                 class="accordion-collapse collapse {% if forloop.first %}show{% endif %}"
                                 aria-labelledby="heading{{ forloop.counter }}"
                                 data-bs-parent="#permissionsAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        {% for permission in permissions %}
                                        <div class="col-md-6 col-lg-4 mb-3">
                                            <div class="form-check permission-item">
                                                <input class="form-check-input" 
                                                       type="checkbox" 
                                                       name="permissions" 
                                                       value="{{ permission.id }}"
                                                       id="permission_{{ permission.id }}"
                                                       {% if permission in user_profile.permissions.all %}checked{% endif %}>
                                                <label class="form-check-label" for="permission_{{ permission.id }}">
                                                    <strong>{{ permission.get_permission_type_display }}</strong>
                                                    {% if permission.description %}
                                                        <br><small class="text-muted">{{ permission.description }}</small>
                                                    {% endif %}
                                                </label>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    
                                    <!-- Module Quick Actions -->
                                    <div class="mt-3 pt-3 border-top">
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-primary me-2" 
                                                onclick="selectModulePermissions('{{ module }}')">
                                            <i class="bi bi-check-square me-1"></i>تحديد الكل
                                        </button>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-secondary" 
                                                onclick="clearModulePermissions('{{ module }}')">
                                            <i class="bi bi-square me-1"></i>إلغاء الكل
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="bi bi-shield-check me-2"></i>
                            حفظ الصلاحيات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar-circle-large {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
    }
    
    .user-avatar img {
        object-fit: cover;
    }
    
    .permission-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }
    
    .permission-item:hover {
        background: #e9ecef;
        transform: translateY(-2px);
    }
    
    .permission-item .form-check-input:checked + .form-check-label {
        color: var(--primary-color);
        font-weight: 600;
    }
    
    .accordion-button:not(.collapsed) {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        color: var(--primary-color);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
function selectAllPermissions() {
    document.querySelectorAll('input[name="permissions"]').forEach(checkbox => {
        checkbox.checked = true;
    });
}

function clearAllPermissions() {
    document.querySelectorAll('input[name="permissions"]').forEach(checkbox => {
        checkbox.checked = false;
    });
}

function selectModulePermissions(module) {
    document.querySelectorAll(`#collapse${getModuleIndex(module)} input[name="permissions"]`).forEach(checkbox => {
        checkbox.checked = true;
    });
}

function clearModulePermissions(module) {
    document.querySelectorAll(`#collapse${getModuleIndex(module)} input[name="permissions"]`).forEach(checkbox => {
        checkbox.checked = false;
    });
}

function getModuleIndex(module) {
    const modules = [
        {% for module, permissions in permissions_by_module.items %}
        '{{ module }}'{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];
    return modules.indexOf(module) + 1;
}

function applyTemplate(templateId) {
    if (confirm('هل أنت متأكد من تطبيق هذا القالب؟ سيتم استبدال الصلاحيات الحالية.')) {
        fetch('{% url "permissions:apply_template_to_user" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: `template_id=${templateId}&user_id={{ user.id }}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}

function editUserInfo(userId) {
    // توجيه لصفحة تعديل المستخدم - تم الإصلاح
    console.log('Redirecting to edit user info:', userId);
    window.location.href = '{% url "system_settings:user_edit" user_id=0 %}'.replace('0', userId);
}
</script>
{% endblock %}




