{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }

    .page-header {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        border-radius: 25px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 900;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-box {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
        border-left: 4px solid var(--color);
    }

    .stat-box.primary { --color: #667eea; }
    .stat-box.success { --color: #56ab2f; }
    .stat-box.warning { --color: #f093fb; }
    .stat-box.info { --color: #74b9ff; }

    .stat-number {
        font-size: 2rem;
        font-weight: 900;
        color: var(--color);
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #636e72;
        font-weight: 600;
    }

    .orders-table {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1.5rem 1rem;
        font-weight: 700;
        text-align: center;
    }

    .table tbody td {
        padding: 1.5rem 1rem;
        vertical-align: middle;
        border-bottom: 1px solid #f1f3f4;
    }

    .table tbody tr:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .order-number {
        font-weight: 700;
        color: #2d3436;
        font-size: 1.1rem;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        text-align: center;
        display: inline-block;
        min-width: 100px;
    }

    .status-draft { background: #e9ecef; color: #495057; }
    .status-approved { background: #d1ecf1; color: #0c5460; }
    .status-in_progress { background: #fff3cd; color: #856404; }
    .status-completed { background: #d4edda; color: #155724; }
    .status-cancelled { background: #f8d7da; color: #721c24; }

    .priority-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .priority-low { background: #d1f2eb; color: #0e6655; }
    .priority-normal { background: #d6eaf8; color: #1b4f72; }
    .priority-high { background: #fadbd8; color: #943126; }
    .priority-urgent { background: #f1948a; color: #ffffff; }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
    }

    .btn-action {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        transition: all 0.3s ease;
        text-decoration: none;
    }

    .btn-view {
        background: linear-gradient(135deg, #74b9ff, #0984e3);
        color: white;
    }

    .btn-edit {
        background: linear-gradient(135deg, #fdcb6e, #e17055);
        color: white;
    }

    .btn-print {
        background: linear-gradient(135deg, #a29bfe, #6c5ce7);
        color: white;
    }

    .btn-delete {
        background: linear-gradient(135deg, #ff7675, #d63031);
        color: white;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        color: white;
        text-decoration: none;
    }

    .create-btn {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        color: white;
        border: none;
        border-radius: 15px;
        padding: 1rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 10px 25px rgba(86, 171, 47, 0.3);
    }

    .create-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(86, 171, 47, 0.4);
        color: white;
        text-decoration: none;
    }

    .filter-form {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        align-items: end;
    }

    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-filter {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-filter:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-list-ul me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'manufacturing:order_create' %}" class="create-btn">
                    <i class="bi bi-plus-circle"></i>
                    إنشاء أمر جديد
                </a>
                <a href="{% url 'manufacturing:dashboard' %}" class="btn btn-outline-light ms-2">
                    <i class="bi bi-arrow-left me-2"></i>العودة
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-row">
        <div class="stat-box primary">
            <div class="stat-number">{{ total_orders }}</div>
            <div class="stat-label">إجمالي الأوامر</div>
        </div>
        <div class="stat-box success">
            <div class="stat-number">{{ total_estimated_cost|floatformat:0 }}</div>
            <div class="stat-label">إجمالي التكلفة المقدرة</div>
        </div>
        <div class="stat-box warning">
            <div class="stat-number">{{ orders|length }}</div>
            <div class="stat-label">الأوامر المعروضة</div>
        </div>
        <div class="stat-box info">
            <div class="stat-number">{{ orders.count }}</div>
            <div class="stat-label">نتائج البحث</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="content-card">
        <div class="filter-section">
            <h4 class="mb-3"><i class="bi bi-funnel me-2"></i>البحث والفلترة</h4>
            <form method="get" class="filter-form">
                <div class="form-group">
                    <label>البحث</label>
                    <input type="text" name="search" class="form-control" value="{{ search_query }}" 
                           placeholder="رقم الأمر أو اسم المنتج">
                </div>
                <div class="form-group">
                    <label>الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                    <label>الأولوية</label>
                    <select name="priority" class="form-select">
                        <option value="">جميع الأولويات</option>
                        {% for value, label in priority_choices %}
                            <option value="{{ value }}" {% if priority_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                    <label>من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="form-group">
                    <label>إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="form-group">
                    <button type="submit" class="btn-filter">
                        <i class="bi bi-search me-2"></i>بحث
                    </button>
                </div>
                <div class="form-group">
                    <a href="{% url 'manufacturing:order_list' %}" class="btn-reset">
                        <i class="bi bi-arrow-clockwise me-2"></i>إعادة تعيين
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="content-card">
        <div class="orders-table">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>رقم الأمر</th>
                            <th>المنتج النهائي</th>
                            <th>الكمية</th>
                            <th>الحالة</th>
                            <th>الأولوية</th>
                            <th>التكلفة المقدرة</th>
                            <th>تاريخ الأمر</th>
                            <th>تاريخ الإنجاز</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders %}
                        <tr>
                            <td>
                                <div class="order-number">{{ order.order_number }}</div>
                            </td>
                            <td>
                                <div class="product-info">
                                    <div class="product-name">{{ order.final_product.name }}</div>
                                    <div class="product-code">{{ order.final_product.code }}</div>
                                </div>
                            </td>
                            <td>
                                <div class="quantity-info">
                                    {{ order.quantity_to_produce }} {{ order.unit_of_measure.name }}
                                </div>
                            </td>
                            <td>
                                <span class="status-badge status-{{ order.status }}">
                                    {{ order.get_status_display }}
                                </span>
                            </td>
                            <td>
                                <span class="priority-badge priority-{{ order.priority }}">
                                    {{ order.get_priority_display }}
                                </span>
                            </td>
                            <td>
                                <div class="cost-amount">
                                    {{ order.total_estimated_cost|floatformat:2 }} جنيه
                                </div>
                            </td>
                            <td>
                                <div class="date-info">
                                    {{ order.order_date|date:"d/m/Y" }}
                                </div>
                            </td>
                            <td>
                                <div class="date-info">
                                    {{ order.expected_completion_date|date:"d/m/Y" }}
                                    {% if order.is_overdue %}
                                        <br><small class="text-danger">متأخر</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="{% url 'manufacturing:order_detail' order.id %}" class="btn-action btn-view" title="عرض">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    {% if order.status in 'draft,approved' %}
                                    <a href="{% url 'manufacturing:order_edit' order.id %}" class="btn-action btn-edit" title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <form method="post" action="{% url 'manufacturing:order_quick_delete' order.id %}" style="display: inline;"
                                          onsubmit="return confirm('هل أنت متأكد من حذف أمر التصنيع {{ order.order_number }}؟')">
                                        {% csrf_token %}
                                        <button type="submit" class="btn-action btn-delete" title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                    {% endif %}
                                    <a href="{% url 'manufacturing:order_print' order.id %}" class="btn-action btn-print" title="طباعة" target="_blank">
                                        <i class="bi bi-printer"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="9" class="text-center py-5">
                                <i class="bi bi-inbox" style="font-size: 3rem; color: #6c757d;"></i>
                                <p class="mt-3 text-muted">لا توجد أوامر تصنيع متاحة</p>
                                <a href="{% url 'manufacturing:order_create' %}" class="create-btn">
                                    <i class="bi bi-plus-circle"></i>
                                    إنشاء أمر تصنيع جديد
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
