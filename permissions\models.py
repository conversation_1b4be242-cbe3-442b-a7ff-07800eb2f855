from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone


class Department(models.Model):
    """الأقسام في الشركة"""
    
    name = models.CharField(max_length=100, unique=True, verbose_name="اسم القسم")
    description = models.TextField(blank=True, verbose_name="وصف القسم")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    
    class Meta:
        verbose_name = "قسم"
        verbose_name_plural = "الأقسام"
        ordering = ['name']
    
    def __str__(self):
        return self.name


class JobPosition(models.Model):
    """المناصب الوظيفية"""
    
    name = models.CharField(max_length=100, unique=True, verbose_name="اسم المنصب")
    department = models.ForeignKey(Department, on_delete=models.CASCADE, verbose_name="القسم")
    description = models.TextField(blank=True, verbose_name="وصف المنصب")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    
    class Meta:
        verbose_name = "منصب وظيفي"
        verbose_name_plural = "المناصب الوظيفية"
        ordering = ['department', 'name']
    
    def __str__(self):
        return f"{self.department.name} - {self.name}"


class Permission(models.Model):
    """الصلاحيات البسيطة"""
    
    # أنواع الصلاحيات
    PERMISSION_TYPES = [
        ('view', 'عرض'),
        ('add', 'إضافة'),
        ('edit', 'تعديل'),
        ('delete', 'حذف'),
        ('export', 'تصدير'),
        ('import', 'استيراد'),
        ('approve', 'موافقة'),
        ('manage', 'إدارة كاملة'),
    ]
    
    # الوحدات/الصفحات
    MODULES = [
        ('dashboard', 'لوحة التحكم'),
        ('definitions', 'التعريفات'),
        ('warehouses', 'إدارة المخازن'),
        ('manufacturing', 'التصنيع'),
        ('sales', 'المبيعات'),
        ('purchases', 'المشتريات'),
        ('assets', 'الأصول الثابتة'),
        ('banks', 'البنوك'),
        ('treasuries', 'الخزائن'),
        ('accounting', 'الحسابات العامة'),
        ('branches', 'المركز الرئيسي والفروع'),
        ('hr', 'شؤون العاملين'),
        ('reports', 'التقارير'),
        ('settings', 'الإعدادات والخدمات'),
        ('users', 'إدارة المستخدمين'),
        ('system', 'إدارة النظام'),
    ]
    
    module = models.CharField(max_length=50, choices=MODULES, verbose_name="الوحدة")
    permission_type = models.CharField(max_length=20, choices=PERMISSION_TYPES, verbose_name="نوع الصلاحية")
    name = models.CharField(max_length=200, verbose_name="اسم الصلاحية")
    description = models.TextField(blank=True, verbose_name="وصف الصلاحية")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    
    class Meta:
        verbose_name = "صلاحية"
        verbose_name_plural = "الصلاحيات"
        unique_together = ['module', 'permission_type']
        ordering = ['module', 'permission_type']
    
    def __str__(self):
        return f"{self.get_module_display()} - {self.get_permission_type_display()}"


class UserProfile(models.Model):
    """ملف المستخدم المبسط"""
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name="المستخدم")
    job_position = models.ForeignKey(JobPosition, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="المنصب الوظيفي")
    permissions = models.ManyToManyField(Permission, blank=True, verbose_name="الصلاحيات")
    
    # معلومات إضافية
    phone = models.CharField(max_length=20, blank=True, verbose_name="رقم الهاتف")
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True, verbose_name="الصورة الشخصية")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    last_login_ip = models.GenericIPAddressField(null=True, blank=True, verbose_name="آخر IP دخول")
    
    class Meta:
        verbose_name = "ملف مستخدم"
        verbose_name_plural = "ملفات المستخدمين"
    
    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username}"
    
    def has_permission(self, module, permission_type):
        """فحص الصلاحية"""
        if self.user.is_superuser:
            return True
        
        return self.permissions.filter(
            module=module,
            permission_type=permission_type,
            is_active=True
        ).exists()
    
    def get_permissions_by_module(self):
        """الحصول على الصلاحيات مجمعة حسب الوحدة"""
        permissions_dict = {}
        for permission in self.permissions.filter(is_active=True):
            if permission.module not in permissions_dict:
                permissions_dict[permission.module] = []
            permissions_dict[permission.module].append(permission.permission_type)
        return permissions_dict


class PermissionTemplate(models.Model):
    """قوالب الصلاحيات للمناصب"""
    
    name = models.CharField(max_length=100, unique=True, verbose_name="اسم القالب")
    job_position = models.ForeignKey(JobPosition, on_delete=models.CASCADE, verbose_name="المنصب الوظيفي")
    permissions = models.ManyToManyField(Permission, verbose_name="الصلاحيات")
    description = models.TextField(blank=True, verbose_name="وصف القالب")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    
    class Meta:
        verbose_name = "قالب صلاحيات"
        verbose_name_plural = "قوالب الصلاحيات"
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} - {self.job_position}"
    
    def apply_to_user(self, user_profile):
        """تطبيق القالب على مستخدم"""
        user_profile.permissions.set(self.permissions.filter(is_active=True))
        user_profile.save()


class LoginLog(models.Model):
    """سجل تسجيل الدخول"""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="المستخدم")
    login_time = models.DateTimeField(auto_now_add=True, verbose_name="وقت الدخول")
    logout_time = models.DateTimeField(null=True, blank=True, verbose_name="وقت الخروج")
    ip_address = models.GenericIPAddressField(verbose_name="عنوان IP")
    user_agent = models.TextField(blank=True, verbose_name="معلومات المتصفح")
    is_successful = models.BooleanField(default=True, verbose_name="نجح الدخول")
    
    class Meta:
        verbose_name = "سجل دخول"
        verbose_name_plural = "سجلات الدخول"
        ordering = ['-login_time']
    
    def __str__(self):
        return f"{self.user.username} - {self.login_time.strftime('%Y-%m-%d %H:%M')}"
    
    @property
    def session_duration(self):
        """مدة الجلسة"""
        if self.logout_time:
            return self.logout_time - self.login_time
        return timezone.now() - self.login_time
