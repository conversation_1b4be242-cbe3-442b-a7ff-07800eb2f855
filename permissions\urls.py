from django.urls import path
from . import views

app_name = 'permissions'

urlpatterns = [
    # لوحة تحكم الصلاحيات
    path('', views.permissions_dashboard, name='dashboard'),
    
    # إدارة صلاحيات المستخدمين (النظام الجديد في system_settings)
    path('users/<int:user_id>/permissions/', views.user_permissions, name='user_permissions'),
    
    # قوالب الصلاحيات
    path('templates/', views.permission_templates, name='permission_templates'),
    path('templates/create/', views.create_permission_template, name='create_permission_template'),
    path('templates/<int:template_id>/details/', views.template_details, name='template_details'),
    path('templates/<int:template_id>/edit/', views.edit_permission_template, name='edit_permission_template'),
    path('templates/apply/', views.apply_template_to_user, name='apply_template_to_user'),
    
    # الأقسام والمناصب
    path('departments/', views.departments_positions, name='departments_positions'),
    path('positions/<int:position_id>/edit/', views.edit_job_position, name='edit_job_position'),
    
    # صلاحياتي
    path('my-permissions/', views.my_permissions, name='my_permissions'),
]
