<!-- Modal لتعديل المنصب -->
<div class="modal-header">
    <h5 class="modal-title">
        <i class="bi bi-pencil-square me-2"></i>
        تعديل المنصب: {{ position.name }}
    </h5>
    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
</div>

<div class="modal-body">
    <form id="editPositionForm" method="post">
        {% csrf_token %}
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">اسم المنصب *</label>
                    <input type="text" 
                           class="form-control" 
                           name="name" 
                           value="{{ position.name }}"
                           placeholder="أدخل اسم المنصب"
                           required>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">القسم</label>
                    <select class="form-select" name="department">
                        <option value="">-- اختر القسم --</option>
                        {% for department in departments %}
                        <option value="{{ department.id }}" 
                                {% if position.department and position.department.id == department.id %}selected{% endif %}>
                            {{ department.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>
        
        <div class="mb-3">
            <label class="form-label">وصف المنصب</label>
            <textarea class="form-control" 
                      name="description" 
                      rows="3"
                      placeholder="أدخل وصف المنصب (اختياري)">{{ position.description }}</textarea>
        </div>
        
        <div class="mb-3">
            <div class="form-check">
                <input class="form-check-input" 
                       type="checkbox" 
                       name="is_active" 
                       id="editPositionActive"
                       {% if position.is_active %}checked{% endif %}>
                <label class="form-check-label" for="editPositionActive">
                    <strong>نشط</strong>
                    <br><small class="text-muted">المنصب متاح للاستخدام</small>
                </label>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="alert alert-info">
            <h6 class="alert-heading">
                <i class="bi bi-info-circle me-2"></i>
                معلومات المنصب
            </h6>
            <div class="row">
                <div class="col-md-6">
                    <small><strong>تاريخ الإنشاء:</strong> {{ position.created_at|date:"Y/m/d H:i" }}</small>
                </div>
                <div class="col-md-6">
                    <small><strong>آخر تحديث:</strong> {{ position.updated_at|date:"Y/m/d H:i" }}</small>
                </div>
            </div>
            {% if position.userprofile_set.exists %}
            <hr class="my-2">
            <small class="text-warning">
                <i class="bi bi-exclamation-triangle me-1"></i>
                يوجد {{ position.userprofile_set.count }} مستخدم مرتبط بهذا المنصب
            </small>
            {% endif %}
        </div>
    </form>
</div>

<div class="modal-footer">
    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
        <i class="bi bi-x-circle me-2"></i>
        إلغاء
    </button>
    <button type="submit" form="editPositionForm" class="btn btn-success">
        <i class="bi bi-check-circle me-2"></i>
        حفظ التغييرات
    </button>
</div>

<style>
.form-check {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    border-left: 4px solid var(--primary-color);
}

.form-check:hover {
    background: #e9ecef;
}

.alert-info {
    background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
    border: 1px solid #bbdefb;
}
</style>

<script>
document.getElementById('editPositionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = document.querySelector('button[type="submit"][form="editPositionForm"]');
    const originalText = submitBtn.innerHTML;
    
    // تعطيل الزر وإظهار حالة التحميل
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
    
    fetch('{% url "permissions:edit_job_position" position.id %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إظهار رسالة نجاح
            showAlert('success', data.message);
            
            // إغلاق Modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('editPositionModal'));
            modal.hide();
            
            // تحديث الصفحة أو البيانات
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في الاتصال');
    })
    .finally(() => {
        // إعادة تفعيل الزر
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

function showAlert(type, message) {
    // إنشاء تنبيه مؤقت
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// التحقق من صحة النموذج
document.querySelector('input[name="name"]').addEventListener('input', function() {
    const value = this.value.trim();
    const submitBtn = document.querySelector('button[type="submit"][form="editPositionForm"]');
    
    if (value.length < 2) {
        this.classList.add('is-invalid');
        submitBtn.disabled = true;
    } else {
        this.classList.remove('is-invalid');
        submitBtn.disabled = false;
    }
});
</script>
