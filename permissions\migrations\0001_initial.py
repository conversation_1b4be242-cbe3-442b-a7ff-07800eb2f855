# Generated by Django 5.2.4 on 2025-07-15 11:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم القسم')),
                ('description', models.TextField(blank=True, verbose_name='وصف القسم')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'قسم',
                'verbose_name_plural': 'الأقسام',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='JobPosition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم المنصب')),
                ('description', models.TextField(blank=True, verbose_name='وصف المنصب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='permissions.department', verbose_name='القسم')),
            ],
            options={
                'verbose_name': 'منصب وظيفي',
                'verbose_name_plural': 'المناصب الوظيفية',
                'ordering': ['department', 'name'],
            },
        ),
        migrations.CreateModel(
            name='LoginLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('login_time', models.DateTimeField(auto_now_add=True, verbose_name='وقت الدخول')),
                ('logout_time', models.DateTimeField(blank=True, null=True, verbose_name='وقت الخروج')),
                ('ip_address', models.GenericIPAddressField(verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, verbose_name='معلومات المتصفح')),
                ('is_successful', models.BooleanField(default=True, verbose_name='نجح الدخول')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل دخول',
                'verbose_name_plural': 'سجلات الدخول',
                'ordering': ['-login_time'],
            },
        ),
        migrations.CreateModel(
            name='Permission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('module', models.CharField(choices=[('dashboard', 'لوحة التحكم'), ('definitions', 'التعريفات'), ('warehouses', 'إدارة المخازن'), ('manufacturing', 'التصنيع'), ('sales', 'المبيعات'), ('purchases', 'المشتريات'), ('assets', 'الأصول الثابتة'), ('banks', 'البنوك'), ('treasuries', 'الخزائن'), ('accounting', 'الحسابات العامة'), ('branches', 'المركز الرئيسي والفروع'), ('hr', 'شؤون العاملين'), ('reports', 'التقارير'), ('settings', 'الإعدادات والخدمات'), ('users', 'إدارة المستخدمين'), ('system', 'إدارة النظام')], max_length=50, verbose_name='الوحدة')),
                ('permission_type', models.CharField(choices=[('view', 'عرض'), ('add', 'إضافة'), ('edit', 'تعديل'), ('delete', 'حذف'), ('export', 'تصدير'), ('import', 'استيراد'), ('approve', 'موافقة'), ('manage', 'إدارة كاملة')], max_length=20, verbose_name='نوع الصلاحية')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الصلاحية')),
                ('description', models.TextField(blank=True, verbose_name='وصف الصلاحية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
            ],
            options={
                'verbose_name': 'صلاحية',
                'verbose_name_plural': 'الصلاحيات',
                'ordering': ['module', 'permission_type'],
                'unique_together': {('module', 'permission_type')},
            },
        ),
        migrations.CreateModel(
            name='PermissionTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم القالب')),
                ('description', models.TextField(blank=True, verbose_name='وصف القالب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('job_position', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='permissions.jobposition', verbose_name='المنصب الوظيفي')),
                ('permissions', models.ManyToManyField(to='permissions.permission', verbose_name='الصلاحيات')),
            ],
            options={
                'verbose_name': 'قالب صلاحيات',
                'verbose_name_plural': 'قوالب الصلاحيات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='رقم الهاتف')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/', verbose_name='الصورة الشخصية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='آخر IP دخول')),
                ('job_position', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='permissions.jobposition', verbose_name='المنصب الوظيفي')),
                ('permissions', models.ManyToManyField(blank=True, to='permissions.permission', verbose_name='الصلاحيات')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'ملف مستخدم',
                'verbose_name_plural': 'ملفات المستخدمين',
            },
        ),
    ]
