{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if user_language == 'en' %}Inventory Management{% else %}إدارة المخزون{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .inventory-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        box-shadow: 0 15px 50px rgba(31, 38, 135, 0.37);
    }

    /* تنسيق أزرار السجلات */
    .btn-group .btn {
        border-radius: 0;
        border-right: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .btn-group .btn:first-child {
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
    }

    .btn-group .btn:last-child {
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
        border-right: none;
    }

    .btn-group .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 2;
    }

    /* ألوان مخصصة للأزرار */
    .btn-success {
        background: linear-gradient(135deg, #28a745, #20c997);
        border: none;
    }

    .btn-danger {
        background: linear-gradient(135deg, #dc3545, #c82333);
        border: none;
    }

    .btn-info {
        background: linear-gradient(135deg, #17a2b8, #138496);
        border: none;
    }

    .btn-primary {
        background: linear-gradient(135deg, #007bff, #0056b3);
        border: none;
    }

    .btn-warning {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        border: none;
        color: #212529;
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6c757d, #5a6268);
        border: none;
    }

    /* تأثيرات إضافية */
    .shadow-sm {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    @media (max-width: 768px) {
        .btn-group {
            flex-direction: column;
            width: 100%;
        }

        .btn-group .btn {
            border-radius: 8px !important;
            margin-bottom: 2px;
            border-right: none;
        }
    }

    .filter-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 10px 40px rgba(31, 38, 135, 0.37);
    }

    .inventory-table {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border-radius: 20px;
        padding: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 10px 40px rgba(31, 38, 135, 0.37);
    }

    /* زر لوحة التحكم الصغير الجميل */
    .dashboard-btn-small {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
        border: none !important;
        border-radius: 12px !important;
        padding: 8px 15px !important;
        font-weight: 600 !important;
        color: white !important;
        text-decoration: none !important;
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3) !important;
        transition: all 0.3s ease !important;
        position: relative;
        overflow: hidden;
        display: inline-flex;
        align-items: center;
    }

    .dashboard-btn-small::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .dashboard-btn-small:hover::before {
        left: 100%;
    }

    .dashboard-btn-small:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4) !important;
        color: white !important;
        text-decoration: none !important;
    }

    .stock-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .status-normal { background: #d4edda; color: #155724; }
    .status-low { background: #fff3cd; color: #856404; }
    .status-out { background: #f8d7da; color: #721c24; }
    .status-high { background: #cce5ff; color: #004085; }

    .btn-inventory {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 0.5rem 1rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-inventory:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
        color: white;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(102, 126, 234, 0.1);
        transform: scale(1.01);
        transition: all 0.2s ease;
    }

    .search-box {
        border-radius: 15px;
        border: 2px solid rgba(102, 126, 234, 0.2);
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .search-box:focus {
        border-color: #667eea;
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        outline: none;
    }

    .filter-select {
        border-radius: 10px;
        border: 2px solid rgba(102, 126, 234, 0.2);
        padding: 0.5rem;
        transition: all 0.3s ease;
    }

    .filter-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 15px rgba(102, 126, 234, 0.2);
        outline: none;
    }
</style>
{% endblock %}

{% block content %}
<!-- Inventory Header -->
<div class="inventory-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-boxes me-2"></i>
                    {% if user_language == 'en' %}Inventory Management{% else %}إدارة المخزون{% endif %}
                </h1>
                <p class="mb-0 opacity-75">{% if user_language == 'en' %}Monitor and manage all inventory items across warehouses{% else %}مراقبة وإدارة جميع عناصر المخزون عبر المخازن{% endif %}</p>
            </div>
            <div class="col-md-8 text-end">
                <!-- أزرار السجلات -->
                <div class="d-flex flex-wrap justify-content-end gap-2 mb-2">
                    <div class="btn-group shadow-sm">
                        <a href="{% url 'warehouses:receive_history' %}" class="btn btn-success btn-sm" title="سجل الإضافات">
                            <i class="bi bi-box-arrow-in-right me-1"></i>
                            <span class="d-none d-md-inline">الإضافات</span>
                        </a>
                        <a href="{% url 'warehouses:issue_history' %}" class="btn btn-danger btn-sm" title="سجل الصرف">
                            <i class="bi bi-box-arrow-right me-1"></i>
                            <span class="d-none d-md-inline">الصرف</span>
                        </a>
                        <a href="{% url 'warehouses:transfer_history' %}" class="btn btn-info btn-sm" title="سجل التحويلات">
                            <i class="bi bi-arrow-left-right me-1"></i>
                            <span class="d-none d-md-inline">التحويلات</span>
                        </a>
                    </div>

                    <div class="btn-group shadow-sm">
                        <a href="{% url 'warehouses:count_history' %}" class="btn btn-primary btn-sm" title="سجل الجرد">
                            <i class="bi bi-clipboard-check me-1"></i>
                            <span class="d-none d-md-inline">الجرد</span>
                        </a>
                        <a href="{% url 'warehouses:adjustments_history' %}" class="btn btn-warning btn-sm" title="سجل التسويات">
                            <i class="bi bi-gear me-1"></i>
                            <span class="d-none d-md-inline">التسويات</span>
                        </a>
                    </div>

                    <!-- زر الإجراءات السريعة -->
                    <div class="dropdown d-inline-block me-2">
                        <button class="btn btn-primary btn-sm dropdown-toggle shadow-sm" type="button" id="quickActionsDropdown" data-bs-toggle="dropdown" aria-expanded="false" title="الإجراءات السريعة">
                            <i class="bi bi-lightning-charge me-1"></i>
                            <span class="d-none d-md-inline">الإجراءات السريعة</span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="quickActionsDropdown">
                            <li><a class="dropdown-item" href="{% url 'warehouses:receive_history' %}">
                                <i class="bi bi-box-arrow-in-right me-2 text-success"></i>إضافة مخزون
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'warehouses:issue_history' %}">
                                <i class="bi bi-box-arrow-right me-2 text-danger"></i>صرف مخزون
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'warehouses:transfer_history' %}">
                                <i class="bi bi-arrow-left-right me-2 text-primary"></i>تحويل مخزون
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'warehouses:count_history' %}">
                                <i class="bi bi-clipboard-check me-2 text-warning"></i>جرد المخزون
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'warehouses:adjustments_history' %}">
                                <i class="bi bi-gear me-2 text-secondary"></i>تسويات المخزون
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'warehouses:stock_reports' %}">
                                <i class="bi bi-graph-up me-2 text-info"></i>تقارير المخزون
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'warehouses:low_stock_alerts' %}">
                                <i class="bi bi-exclamation-triangle me-2 text-warning"></i>تنبيهات المخزون
                            </a></li>
                        </ul>
                    </div>

                    <a href="{% url 'warehouses:dashboard' %}" class="btn dashboard-btn-small shadow-sm" title="لوحة التحكم">
                        <i class="bi bi-speedometer2 me-1"></i>
                        <span class="d-none d-md-inline">{% if user_language == 'en' %}Dashboard{% else %}لوحة التحكم{% endif %}</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Filters Section -->
    <div class="filter-section">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label fw-bold">
                    <i class="bi bi-search me-1"></i>البحث
                </label>
                <input type="text" 
                       name="search" 
                       class="form-control search-box" 
                       placeholder="ابحث بالاسم أو الكود..."
                       value="{{ search_query }}">
            </div>
            
            <div class="col-md-3">
                <label class="form-label fw-bold">
                    <i class="bi bi-building me-1"></i>المخزن
                </label>
                <select name="warehouse" class="form-select filter-select">
                    <option value="">جميع المخازن</option>
                    {% for warehouse in warehouses %}
                    <option value="{{ warehouse.id }}" {% if selected_warehouse == warehouse.id|stringformat:"s" %}selected{% endif %}>
                        {{ warehouse.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-3">
                <label class="form-label fw-bold">
                    <i class="bi bi-speedometer2 me-1"></i>حالة المخزون
                </label>
                <select name="status" class="form-select filter-select">
                    <option value="">جميع الحالات</option>
                    <option value="normal" {% if stock_status == 'normal' %}selected{% endif %}>طبيعي</option>
                    <option value="low" {% if stock_status == 'low' %}selected{% endif %}>منخفض</option>
                    <option value="out" {% if stock_status == 'out' %}selected{% endif %}>نافد</option>
                </select>
            </div>
            
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-inventory w-100">
                    <i class="bi bi-funnel me-1"></i>فلترة
                </button>
            </div>
        </form>
    </div>

    <!-- Inventory Table -->
    <div class="inventory-table">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h3 class="mb-0">
                <i class="bi bi-list-ul me-2"></i>قائمة المخزون
            </h3>
            <div class="text-muted">
                إجمالي العناصر: <strong>{{ inventory_items.count }}</strong>
            </div>
        </div>
        
        {% if inventory_items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>المنتج</th>
                        <th>المخزن</th>
                        <th>الموقع</th>
                        <th>الكمية المتاحة</th>
                        <th>الكمية المحجوزة</th>
                        <th>الحد الأدنى</th>
                        <th>متوسط التكلفة</th>
                        <th>إجمالي القيمة</th>
                        <th>الحالة</th>
                        <th>آخر حركة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in inventory_items %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ item.product.name }}</strong>
                                <br><small class="text-muted">{{ item.product.code }}</small>
                            </div>
                        </td>
                        <td>{{ item.warehouse.name }}</td>
                        <td>{{ item.location.name|default:"غير محدد" }}</td>
                        <td>
                            <strong class="{% if item.quantity_on_hand <= 0 %}text-danger{% elif item.is_low_stock %}text-warning{% else %}text-success{% endif %}">
                                {{ item.quantity_on_hand }}
                            </strong>
                        </td>
                        <td>{{ item.quantity_reserved }}</td>
                        <td>{{ item.minimum_stock }}</td>
                        <td>
                            <strong class="text-primary">{{ item.average_cost|default:item.product.cost_price|floatformat:2 }} ج.م</strong>
                        </td>
                        <td>
                            <strong class="text-success">{{ item.total_value|floatformat:2 }} ج.م</strong>
                        </td>
                        <td>
                            <span class="stock-status status-{% if item.is_out_of_stock %}out{% elif item.is_low_stock %}low{% elif item.quantity_on_hand >= item.maximum_stock %}high{% else %}normal{% endif %}">
                                {{ item.stock_status }}
                            </span>
                        </td>
                        <td>
                            {% if item.last_received_date %}
                                {{ item.last_received_date|date:"Y-m-d" }}
                            {% else %}
                                <span class="text-muted">لا يوجد</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'warehouses:inventory_detail' item.id %}" class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{% url 'warehouses:stock_receive' %}?product={{ item.product.id }}&warehouse={{ item.warehouse.id }}" class="btn btn-outline-success" title="إضافة مخزون">
                                    <i class="bi bi-plus"></i>
                                </a>
                                <a href="{% url 'warehouses:stock_issue' %}?product={{ item.product.id }}&warehouse={{ item.warehouse.id }}" class="btn btn-outline-danger" title="صرف مخزون">
                                    <i class="bi bi-dash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-inbox display-1 text-muted"></i>
            <h4 class="mt-3 text-muted">لا توجد عناصر مخزون</h4>
            <p class="text-muted">لم يتم العثور على عناصر مخزون تطابق معايير البحث.</p>
            <a href="{% url 'warehouses:stock_receive' %}" class="btn btn-inventory">
                <i class="bi bi-plus-circle me-2"></i>إضافة مخزون جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
