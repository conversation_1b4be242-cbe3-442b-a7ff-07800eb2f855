{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    
    .manufacturing-header {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        border-radius: 25px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        position: relative;
        overflow: hidden;
    }
    
    .manufacturing-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }
    
    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 900;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }
    
    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .alert-custom {
        border-radius: 15px;
        border: none;
        padding: 1.5rem;
        margin: 1.5rem 0;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }
    
    .alert-success-custom {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
        border-left: 4px solid #28a745;
    }
    
    .alert-warning-custom {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        color: #856404;
        border-left: 4px solid #ffc107;
    }
    
    .alert-danger-custom {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
        border-left: 4px solid #dc3545;
    }
    
    .materials-check-table {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        margin: 2rem 0;
    }
    
    .materials-check-table .table {
        margin: 0;
    }
    
    .materials-check-table .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1.5rem 1rem;
        font-weight: 700;
        text-align: center;
    }
    
    .materials-check-table .table tbody td {
        padding: 1.5rem 1rem;
        vertical-align: middle;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .materials-check-table .table tbody tr:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }
    
    .availability-badge {
        padding: 0.5rem 1rem;
        border-radius: 15px;
        font-size: 0.9rem;
        font-weight: 600;
        display: inline-block;
        min-width: 80px;
        text-align: center;
    }
    
    .available { 
        background: linear-gradient(135deg, #d4edda, #c3e6cb); 
        color: #155724; 
    }
    .unavailable { 
        background: linear-gradient(135deg, #f8d7da, #f5c6cb); 
        color: #721c24; 
    }
    .partial { 
        background: linear-gradient(135deg, #fff3cd, #ffeaa7); 
        color: #856404; 
    }
    
    .action-buttons {
        display: flex;
        gap: 1.5rem;
        justify-content: center;
        margin: 3rem 0;
        flex-wrap: wrap;
    }
    
    .btn-action {
        border-radius: 15px;
        padding: 1.2rem 3rem;
        font-size: 1.2rem;
        font-weight: 700;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        min-width: 200px;
        justify-content: center;
    }
    
    .btn-start-production {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        color: white;
        box-shadow: 0 12px 30px rgba(86, 171, 47, 0.4);
    }
    
    .btn-start-production:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(86, 171, 47, 0.5);
        color: white;
        text-decoration: none;
    }
    
    .btn-start-production:disabled {
        background: linear-gradient(135deg, #6c757d, #495057);
        cursor: not-allowed;
        transform: none;
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
    }
    
    .btn-cancel {
        background: linear-gradient(135deg, #636e72 0%, #495057 100%);
        color: white;
        box-shadow: 0 10px 25px rgba(99, 110, 114, 0.3);
    }
    
    .btn-cancel:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(99, 110, 114, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .order-summary {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 20px;
        padding: 2rem;
        margin: 2rem 0;
        text-align: center;
    }
    
    .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
        margin-top: 1.5rem;
    }
    
    .summary-item {
        background: rgba(255, 255, 255, 0.8);
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .summary-label {
        font-size: 1rem;
        color: #1565c0;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .summary-value {
        font-size: 1.5rem;
        font-weight: 900;
        color: #0d47a1;
    }
    
    .section-title {
        font-size: 1.5rem;
        font-weight: 800;
        color: #2d3436;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .section-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
    }
    
    .warning-box {
        background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
        border: 2px solid #ffb74d;
        border-radius: 15px;
        padding: 2rem;
        margin: 2rem 0;
        text-align: center;
    }
    
    .warning-icon {
        font-size: 3rem;
        color: #ff9800;
        margin-bottom: 1rem;
    }
    
    .warning-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #e65100;
        margin-bottom: 1rem;
    }
    
    .warning-text {
        font-size: 1.1rem;
        color: #bf360c;
        line-height: 1.6;
    }
    
    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }
        
        .action-buttons {
            flex-direction: column;
            align-items: center;
        }
        
        .btn-action {
            width: 100%;
            max-width: 300px;
        }
        
        .summary-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="manufacturing-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-play-circle me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'manufacturing:order_detail' order.id %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة لتفاصيل الأمر
                </a>
            </div>
        </div>
    </div>

    <!-- ملخص الأمر -->
    <div class="content-card">
        <h2 class="section-title">
            <div class="section-icon">
                <i class="bi bi-info-circle"></i>
            </div>
            ملخص أمر التصنيع
        </h2>
        
        <div class="order-summary">
            <h4>{{ order.order_number }} - {{ order.final_product.name }}</h4>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-label">الكمية المطلوبة</div>
                    <div class="summary-value">{{ order.quantity_to_produce }} {{ order.unit_of_measure.name }}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">مخزن المواد الخام</div>
                    <div class="summary-value">{{ order.raw_materials_warehouse.name }}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">مخزن المنتجات التامة</div>
                    <div class="summary-value">{{ order.finished_goods_warehouse.name }}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">التكلفة المقدرة</div>
                    <div class="summary-value">{{ order.total_estimated_cost|floatformat:2 }} جنيه</div>
                </div>
            </div>
        </div>
    </div>

    <!-- فحص توفر المواد الخام -->
    <div class="content-card">
        <h2 class="section-title">
            <div class="section-icon">
                <i class="bi bi-clipboard-check"></i>
            </div>
            فحص توفر المواد الخام
        </h2>
        
        {% if availability_check.all_available %}
            <div class="alert-success-custom">
                <h5><i class="bi bi-check-circle me-2"></i>جميع المواد الخام متوفرة!</h5>
                <p class="mb-0">يمكن بدء الإنتاج فوراً. سيتم خصم المواد الخام من المخزون تلقائياً.</p>
            </div>
        {% else %}
            <div class="alert-warning-custom">
                <h5><i class="bi bi-exclamation-triangle me-2"></i>تحذير: بعض المواد الخام غير متوفرة</h5>
                <p class="mb-0">المواد غير المتوفرة: {{ availability_check.unavailable_materials|join:", " }}</p>
                <p class="mb-0">يمكن المتابعة ولكن قد تحتاج لتوفير المواد الناقصة لاحقاً.</p>
            </div>
        {% endif %}
        
        <div class="materials-check-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>المادة الخام</th>
                        <th>الكمية المطلوبة</th>
                        <th>الكمية المتوفرة</th>
                        <th>وحدة القياس</th>
                        <th>حالة التوفر</th>
                        <th>النقص</th>
                    </tr>
                </thead>
                <tbody>
                    {% for material_status in availability_check.materials_status %}
                    <tr>
                        <td>
                            <div class="fw-bold">{{ material_status.raw_material.raw_material.name }}</div>
                            <small class="text-muted">{{ material_status.raw_material.raw_material.code }}</small>
                            {% if material_status.raw_material.is_critical %}
                                <span class="badge bg-danger ms-2">حرجة</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <strong>{{ material_status.required_quantity }}</strong>
                        </td>
                        <td class="text-center">
                            <strong>{{ material_status.available_quantity }}</strong>
                        </td>
                        <td class="text-center">{{ material_status.raw_material.unit_of_measure.name }}</td>
                        <td class="text-center">
                            {% if material_status.is_available %}
                                <span class="availability-badge available">متوفرة</span>
                            {% elif material_status.shortage > 0 %}
                                <span class="availability-badge partial">نقص جزئي</span>
                            {% else %}
                                <span class="availability-badge unavailable">غير متوفرة</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if material_status.shortage > 0 %}
                                <span class="text-danger fw-bold">{{ material_status.shortage }}</span>
                            {% else %}
                                <span class="text-success">-</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- تحذير مهم -->
    {% if not availability_check.all_available %}
    <div class="warning-box">
        <div class="warning-icon">
            <i class="bi bi-exclamation-triangle-fill"></i>
        </div>
        <div class="warning-title">تحذير مهم</div>
        <div class="warning-text">
            بدء الإنتاج سيؤدي إلى خصم المواد الخام المتوفرة من المخزون فوراً.<br>
            تأكد من توفير المواد الناقصة قبل المتابعة لتجنب توقف الإنتاج.
        </div>
    </div>
    {% endif %}

    <!-- أزرار الإجراءات -->
    <div class="action-buttons">
        <form method="post" style="display: inline;">
            {% csrf_token %}
            <input type="submit" value="بدء الإنتاج وخصم المواد" class="btn-action btn-start-production">
        </form>

        <a href="{% url 'manufacturing:order_detail' order.id %}" class="btn-action btn-cancel">
            إلغاء
        </a>
    </div>
</div>
{% endblock %}
