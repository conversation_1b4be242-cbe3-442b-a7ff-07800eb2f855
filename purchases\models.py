from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from decimal import Decimal

class Supplier(models.Model):
    """نموذج الموردين"""
    name = models.CharField(max_length=200, verbose_name="اسم المورد")
    email = models.EmailField(blank=True, null=True, verbose_name="البريد الإلكتروني")
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="رقم الهاتف")
    address = models.TextField(blank=True, null=True, verbose_name="العنوان")
    tax_number = models.CharField(max_length=50, blank=True, null=True, verbose_name="الرقم الضريبي")
    payment_terms = models.CharField(max_length=100, blank=True, null=True, verbose_name="شروط الدفع")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "مورد"
        verbose_name_plural = "الموردين"
        ordering = ['name']

    def __str__(self):
        return self.name

class Product(models.Model):
    """نموذج المنتجات للمشتريات"""
    name = models.CharField(max_length=200, verbose_name="اسم المنتج")
    code = models.CharField(max_length=50, unique=True, verbose_name="كود المنتج")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    purchase_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر الشراء")
    selling_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر البيع")
    stock_quantity = models.IntegerField(default=0, verbose_name="الكمية المتاحة")
    min_stock_level = models.IntegerField(default=0, verbose_name="الحد الأدنى للمخزون")
    unit = models.CharField(max_length=20, default="قطعة", verbose_name="الوحدة")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "منتج"
        verbose_name_plural = "المنتجات"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

    @property
    def needs_reorder(self):
        return self.stock_quantity <= self.min_stock_level

class PurchaseOrder(models.Model):
    """نموذج أوامر الشراء"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('sent', 'مرسل'),
        ('confirmed', 'مؤكد'),
        ('received', 'تم الاستلام'),
        ('cancelled', 'ملغي'),
    ]

    order_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الطلب")
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, verbose_name="المورد")
    order_date = models.DateField(verbose_name="تاريخ الطلب")
    expected_delivery_date = models.DateField(blank=True, null=True, verbose_name="تاريخ التسليم المتوقع")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="الحالة")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم")
    tax_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=15, verbose_name="نسبة الضريبة")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "أمر شراء"
        verbose_name_plural = "أوامر الشراء"
        ordering = ['-created_at']

    def __str__(self):
        return f"أمر شراء {self.order_number} - {self.supplier.name}"

    @property
    def subtotal(self):
        return sum(item.total_price for item in self.items.all())

    @property
    def discount_amount(self):
        return self.subtotal * (self.discount_percentage / 100)

    @property
    def tax_amount(self):
        return (self.subtotal - self.discount_amount) * (self.tax_percentage / 100)

    @property
    def total_amount(self):
        return self.subtotal - self.discount_amount + self.tax_amount

class PurchaseOrderItem(models.Model):
    """نموذج عناصر أمر الشراء"""
    order = models.ForeignKey(PurchaseOrder, related_name='items', on_delete=models.CASCADE, verbose_name="أمر الشراء")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0.01)], verbose_name="الكمية")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر الوحدة")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم")

    class Meta:
        verbose_name = "عنصر أمر شراء"
        verbose_name_plural = "عناصر أوامر الشراء"

    def __str__(self):
        return f"{self.product.name} - {self.quantity} {self.product.unit}"

    @property
    def total_price(self):
        subtotal = self.quantity * self.unit_price
        discount = subtotal * (self.discount_percentage / 100)
        return subtotal - discount

class PurchaseInvoice(models.Model):
    """نموذج فواتير الشراء"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('received', 'مستلمة'),
        ('paid', 'مدفوعة'),
        ('overdue', 'متأخرة'),
        ('cancelled', 'ملغية'),
    ]

    invoice_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الفاتورة")
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, verbose_name="المورد")
    order = models.ForeignKey(PurchaseOrder, blank=True, null=True, on_delete=models.SET_NULL, verbose_name="أمر الشراء")
    invoice_date = models.DateField(verbose_name="تاريخ الفاتورة")
    due_date = models.DateField(verbose_name="تاريخ الاستحقاق")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="الحالة")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم")
    tax_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=15, verbose_name="نسبة الضريبة")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "فاتورة شراء"
        verbose_name_plural = "فواتير الشراء"
        ordering = ['-created_at']

    def __str__(self):
        return f"فاتورة {self.invoice_number} - {self.supplier.name}"

    @property
    def subtotal(self):
        return sum(item.total_price for item in self.items.all())

    @property
    def discount_amount(self):
        return self.subtotal * (self.discount_percentage / 100)

    @property
    def tax_amount(self):
        return (self.subtotal - self.discount_amount) * (self.tax_percentage / 100)

    @property
    def total_amount(self):
        return self.subtotal - self.discount_amount + self.tax_amount

class PurchaseInvoiceItem(models.Model):
    """نموذج عناصر فاتورة الشراء"""
    invoice = models.ForeignKey(PurchaseInvoice, related_name='items', on_delete=models.CASCADE, verbose_name="فاتورة الشراء")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0.01)], verbose_name="الكمية")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر الوحدة")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم")

    class Meta:
        verbose_name = "عنصر فاتورة شراء"
        verbose_name_plural = "عناصر فواتير الشراء"

    def __str__(self):
        return f"{self.product.name} - {self.quantity} {self.product.unit}"

    @property
    def total_price(self):
        subtotal = self.quantity * self.unit_price
        discount = subtotal * (self.discount_percentage / 100)
        return subtotal - discount
