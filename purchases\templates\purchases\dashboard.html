{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة تحكم المشتريات{% endblock %}

{% block extra_css %}
<style>
    .purchases-header {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 1rem 1rem;
    }

    .stats-card {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border: none;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stats-label {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 0;
    }

    .quick-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        margin-bottom: 2rem;
    }

    .quick-action-btn {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        color: white;
        padding: 1rem 2rem;
        border-radius: 0.5rem;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .quick-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(220,53,69,0.3);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<!-- Purchases Header -->
<div class="purchases-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-cart-plus me-2"></i>
                    لوحة تحكم المشتريات
                </h1>
                <p class="mb-0 opacity-75">إدارة شاملة لعمليات الشراء والموردين والفواتير</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="text-white">
                    <i class="bi bi-calendar me-2"></i>
                    {{ "now"|date:"Y/m/d" }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="container-fluid">
    <div class="quick-actions">
        <a href="#" class="quick-action-btn">
            <i class="bi bi-plus-circle"></i>
            طلب شراء جديد
        </a>
        <a href="#" class="quick-action-btn">
            <i class="bi bi-receipt"></i>
            فاتورة شراء جديدة
        </a>
        <a href="#" class="quick-action-btn">
            <i class="bi bi-people"></i>
            إدارة الموردين
        </a>
        <a href="#" class="quick-action-btn">
            <i class="bi bi-file-earmark-text"></i>
            تقارير المشتريات
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(220, 53, 69, 0.1); color: #dc3545;">
                    <i class="bi bi-cart-plus"></i>
                </div>
                <div class="stats-number" style="color: #dc3545;">{{ total_orders }}</div>
                <p class="stats-label">إجمالي طلبات الشراء</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(40, 167, 69, 0.1); color: #28a745;">
                    <i class="bi bi-receipt"></i>
                </div>
                <div class="stats-number text-success">{{ total_invoices }}</div>
                <p class="stats-label">إجمالي الفواتير</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(0, 123, 255, 0.1); color: #007bff;">
                    <i class="bi bi-people"></i>
                </div>
                <div class="stats-number text-primary">{{ total_suppliers }}</div>
                <p class="stats-label">إجمالي الموردين</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(255, 193, 7, 0.1); color: #ffc107;">
                    <i class="bi bi-clock"></i>
                </div>
                <div class="stats-number text-warning">{{ pending_orders }}</div>
                <p class="stats-label">طلبات معلقة</p>
            </div>
        </div>
    </div>

    <!-- Financial Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(32, 201, 151, 0.1); color: #20c997;">
                    <i class="bi bi-currency-dollar"></i>
                </div>
                <div class="stats-number text-info">{{ total_purchases|floatformat:2 }}</div>
                <p class="stats-label">إجمالي المشتريات (ريال)</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(255, 193, 7, 0.1); color: #ffc107;">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <div class="stats-number text-warning">{{ pending_invoices }}</div>
                <p class="stats-label">فواتير معلقة</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(220, 53, 69, 0.1); color: #dc3545;">
                    <i class="bi bi-arrow-down"></i>
                </div>
                <div class="stats-number text-danger">{{ overdue_invoices }}</div>
                <p class="stats-label">فواتير متأخرة</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(111, 66, 193, 0.1); color: #6f42c1;">
                    <i class="bi bi-calendar-month"></i>
                </div>
                <div class="stats-number" style="color: #6f42c1;">{{ monthly_purchases }}</div>
                <p class="stats-label">مشتريات هذا الشهر</p>
            </div>
        </div>
    </div>

    <!-- Recent Orders and Invoices -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أحدث طلبات الشراء</h5>
                    <a href="#" class="btn btn-outline-primary btn-sm">عرض الكل</a>
                </div>
                <div class="card-body">
                    {% if recent_orders %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>المورد</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in recent_orders %}
                                <tr>
                                    <td>{{ order.order_number }}</td>
                                    <td>{{ order.supplier.name }}</td>
                                    <td>{{ order.order_date }}</td>
                                    <td>{{ order.total_amount|floatformat:2 }} ريال</td>
                                    <td>
                                        {% if order.status == 'completed' %}
                                            <span class="badge bg-success">مكتمل</span>
                                        {% elif order.status == 'pending' %}
                                            <span class="badge bg-warning">معلق</span>
                                        {% elif order.status == 'cancelled' %}
                                            <span class="badge bg-danger">ملغي</span>
                                        {% else %}
                                            <span class="badge bg-info">{{ order.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-cart-plus text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">لا توجد طلبات شراء حتى الآن</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أحدث الفواتير</h5>
                    <a href="#" class="btn btn-outline-primary btn-sm">عرض الكل</a>
                </div>
                <div class="card-body">
                    {% if recent_invoices %}
                    <div class="list-group list-group-flush">
                        {% for invoice in recent_invoices %}
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ invoice.invoice_number }}</h6>
                                    <p class="mb-1 small">{{ invoice.supplier.name }}</p>
                                    <small class="text-muted">{{ invoice.invoice_date }}</small>
                                </div>
                                <div>
                                    <div class="text-end">
                                        <strong>{{ invoice.total_amount|floatformat:2 }} ريال</strong>
                                    </div>
                                    {% if invoice.status == 'paid' %}
                                        <span class="badge bg-success">مدفوع</span>
                                    {% elif invoice.status == 'pending' %}
                                        <span class="badge bg-warning">معلق</span>
                                    {% else %}
                                        <span class="badge bg-danger">متأخر</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-receipt text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">لا توجد فواتير حتى الآن</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
