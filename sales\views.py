from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count, Q
from django.http import JsonResponse
from django.core.paginator import Paginator
from datetime import datetime, timedelta
from .models import Customer, Product, SalesOrder, SalesOrderItem, SalesInvoice, SalesInvoiceItem
from .forms import (CustomerForm, ProductForm, SalesOrderForm, SalesOrderItemFormSet,
                   SalesInvoiceForm, SalesInvoiceItemFormSet)

@login_required
def sales_dashboard(request):
    """لوحة تحكم المبيعات"""
    # إحصائيات عامة
    total_customers = Customer.objects.filter(is_active=True).count()
    total_products = Product.objects.filter(is_active=True).count()
    total_orders = SalesOrder.objects.count()
    total_invoices = SalesInvoice.objects.count()

    # إحصائيات مالية
    paid_invoices = SalesInvoice.objects.filter(status='paid')
    total_sales = sum(invoice.total_amount for invoice in paid_invoices)
    pending_invoices = SalesInvoice.objects.filter(status='sent').count()
    overdue_invoices = SalesInvoice.objects.filter(
        status='overdue', due_date__lt=datetime.now().date()).count()

    # أحدث الطلبات
    recent_orders = SalesOrder.objects.select_related('customer').order_by('-created_at')[:5]

    # أحدث الفواتير
    recent_invoices = SalesInvoice.objects.select_related('customer').order_by('-created_at')[:5]

    context = {
        'total_customers': total_customers,
        'total_products': total_products,
        'total_orders': total_orders,
        'total_invoices': total_invoices,
        'total_sales': total_sales,
        'pending_invoices': pending_invoices,
        'overdue_invoices': overdue_invoices,
        'recent_orders': recent_orders,
        'recent_invoices': recent_invoices,
    }
    return render(request, 'sales/dashboard.html', context)

# ========== إدارة العملاء ==========
@login_required
def customer_list(request):
    """قائمة العملاء"""
    search = request.GET.get('search', '')
    customers = Customer.objects.all()

    if search:
        customers = customers.filter(
            Q(name__icontains=search) |
            Q(email__icontains=search) |
            Q(phone__icontains=search)
        )

    paginator = Paginator(customers, 20)
    page_number = request.GET.get('page')
    customers = paginator.get_page(page_number)

    context = {
        'customers': customers,
        'search': search,
    }
    return render(request, 'sales/customer_list.html', context)

@login_required
def customer_create(request):
    """إضافة عميل جديد"""
    if request.method == 'POST':
        form = CustomerForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة العميل بنجاح')
            return redirect('sales:customer_list')
    else:
        form = CustomerForm()

    context = {'form': form, 'title': 'إضافة عميل جديد'}
    return render(request, 'sales/customer_form.html', context)

@login_required
def customer_edit(request, pk):
    """تعديل عميل"""
    customer = get_object_or_404(Customer, pk=pk)
    if request.method == 'POST':
        form = CustomerForm(request.POST, instance=customer)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات العميل بنجاح')
            return redirect('sales:customer_list')
    else:
        form = CustomerForm(instance=customer)

    context = {'form': form, 'title': 'تعديل العميل', 'customer': customer}
    return render(request, 'sales/customer_form.html', context)

@login_required
def customer_delete(request, pk):
    """حذف عميل"""
    customer = get_object_or_404(Customer, pk=pk)
    if request.method == 'POST':
        customer.delete()
        messages.success(request, 'تم حذف العميل بنجاح')
        return redirect('sales:customer_list')

    context = {'customer': customer}
    return render(request, 'sales/customer_confirm_delete.html', context)

# ========== إدارة المنتجات ==========
@login_required
def product_list(request):
    """قائمة المنتجات"""
    search = request.GET.get('search', '')
    products = Product.objects.all()

    if search:
        products = products.filter(
            Q(name__icontains=search) |
            Q(code__icontains=search)
        )

    paginator = Paginator(products, 20)
    page_number = request.GET.get('page')
    products = paginator.get_page(page_number)

    context = {
        'products': products,
        'search': search,
    }
    return render(request, 'sales/product_list.html', context)

@login_required
def product_create(request):
    """إضافة منتج جديد"""
    if request.method == 'POST':
        form = ProductForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة المنتج بنجاح')
            return redirect('sales:product_list')
    else:
        form = ProductForm()

    context = {'form': form, 'title': 'إضافة منتج جديد'}
    return render(request, 'sales/product_form.html', context)

@login_required
def product_edit(request, pk):
    """تعديل منتج"""
    product = get_object_or_404(Product, pk=pk)
    if request.method == 'POST':
        form = ProductForm(request.POST, instance=product)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات المنتج بنجاح')
            return redirect('sales:product_list')
    else:
        form = ProductForm(instance=product)

    context = {'form': form, 'title': 'تعديل المنتج', 'product': product}
    return render(request, 'sales/product_form.html', context)

@login_required
def product_delete(request, pk):
    """حذف منتج"""
    product = get_object_or_404(Product, pk=pk)
    if request.method == 'POST':
        product.delete()
        messages.success(request, 'تم حذف المنتج بنجاح')
        return redirect('sales:product_list')

    context = {'product': product}
    return render(request, 'sales/product_confirm_delete.html', context)

# ========== إدارة أوامر البيع ==========
@login_required
def order_list(request):
    """قائمة أوامر البيع"""
    search = request.GET.get('search', '')
    status = request.GET.get('status', '')
    orders = SalesOrder.objects.select_related('customer', 'created_by')

    if search:
        orders = orders.filter(
            Q(order_number__icontains=search) |
            Q(customer__name__icontains=search)
        )

    if status:
        orders = orders.filter(status=status)

    paginator = Paginator(orders, 20)
    page_number = request.GET.get('page')
    orders = paginator.get_page(page_number)

    context = {
        'orders': orders,
        'search': search,
        'status': status,
        'status_choices': SalesOrder.STATUS_CHOICES,
    }
    return render(request, 'sales/order_list.html', context)

@login_required
def order_create(request):
    """إضافة أمر بيع جديد"""
    if request.method == 'POST':
        form = SalesOrderForm(request.POST)
        formset = SalesOrderItemFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            order = form.save(commit=False)
            order.created_by = request.user
            # إنشاء رقم الطلب تلقائياً
            order.order_number = f"SO-{datetime.now().strftime('%Y%m%d')}-{SalesOrder.objects.count() + 1:04d}"
            order.save()

            formset.instance = order
            formset.save()

            messages.success(request, 'تم إضافة أمر البيع بنجاح')
            return redirect('sales:order_list')
    else:
        form = SalesOrderForm()
        formset = SalesOrderItemFormSet()

    context = {
        'form': form,
        'formset': formset,
        'title': 'إضافة أمر بيع جديد'
    }
    return render(request, 'sales/order_form.html', context)

@login_required
def order_edit(request, pk):
    """تعديل أمر بيع"""
    order = get_object_or_404(SalesOrder, pk=pk)
    if request.method == 'POST':
        form = SalesOrderForm(request.POST, instance=order)
        formset = SalesOrderItemFormSet(request.POST, instance=order)

        if form.is_valid() and formset.is_valid():
            form.save()
            formset.save()
            messages.success(request, 'تم تحديث أمر البيع بنجاح')
            return redirect('sales:order_list')
    else:
        form = SalesOrderForm(instance=order)
        formset = SalesOrderItemFormSet(instance=order)

    context = {
        'form': form,
        'formset': formset,
        'title': 'تعديل أمر البيع',
        'order': order
    }
    return render(request, 'sales/order_form.html', context)

@login_required
def order_delete(request, pk):
    """حذف أمر بيع"""
    order = get_object_or_404(SalesOrder, pk=pk)
    if request.method == 'POST':
        order.delete()
        messages.success(request, 'تم حذف أمر البيع بنجاح')
        return redirect('sales:order_list')

    context = {'order': order}
    return render(request, 'sales/order_confirm_delete.html', context)

# ========== إدارة فواتير البيع ==========
@login_required
def invoice_list(request):
    """قائمة فواتير البيع"""
    search = request.GET.get('search', '')
    status = request.GET.get('status', '')
    invoices = SalesInvoice.objects.select_related('customer', 'created_by')

    if search:
        invoices = invoices.filter(
            Q(invoice_number__icontains=search) |
            Q(customer__name__icontains=search)
        )

    if status:
        invoices = invoices.filter(status=status)

    paginator = Paginator(invoices, 20)
    page_number = request.GET.get('page')
    invoices = paginator.get_page(page_number)

    context = {
        'invoices': invoices,
        'search': search,
        'status': status,
        'status_choices': SalesInvoice.STATUS_CHOICES,
    }
    return render(request, 'sales/invoice_list.html', context)

@login_required
def invoice_create(request):
    """إضافة فاتورة بيع جديدة"""
    if request.method == 'POST':
        form = SalesInvoiceForm(request.POST)
        formset = SalesInvoiceItemFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            invoice = form.save(commit=False)
            invoice.created_by = request.user
            # إنشاء رقم الفاتورة تلقائياً
            invoice.invoice_number = f"INV-{datetime.now().strftime('%Y%m%d')}-{SalesInvoice.objects.count() + 1:04d}"
            invoice.save()

            formset.instance = invoice
            formset.save()

            messages.success(request, 'تم إضافة فاتورة البيع بنجاح')
            return redirect('sales:invoice_list')
    else:
        form = SalesInvoiceForm()
        formset = SalesInvoiceItemFormSet()

    context = {
        'form': form,
        'formset': formset,
        'title': 'إضافة فاتورة بيع جديدة'
    }
    return render(request, 'sales/invoice_form.html', context)

@login_required
def invoice_edit(request, pk):
    """تعديل فاتورة بيع"""
    invoice = get_object_or_404(SalesInvoice, pk=pk)
    if request.method == 'POST':
        form = SalesInvoiceForm(request.POST, instance=invoice)
        formset = SalesInvoiceItemFormSet(request.POST, instance=invoice)

        if form.is_valid() and formset.is_valid():
            form.save()
            formset.save()
            messages.success(request, 'تم تحديث فاتورة البيع بنجاح')
            return redirect('sales:invoice_list')
    else:
        form = SalesInvoiceForm(instance=invoice)
        formset = SalesInvoiceItemFormSet(instance=invoice)

    context = {
        'form': form,
        'formset': formset,
        'title': 'تعديل فاتورة البيع',
        'invoice': invoice
    }
    return render(request, 'sales/invoice_form.html', context)

@login_required
def invoice_delete(request, pk):
    """حذف فاتورة بيع"""
    invoice = get_object_or_404(SalesInvoice, pk=pk)
    if request.method == 'POST':
        invoice.delete()
        messages.success(request, 'تم حذف فاتورة البيع بنجاح')
        return redirect('sales:invoice_list')

    context = {'invoice': invoice}
    return render(request, 'sales/invoice_confirm_delete.html', context)

# ========== API للحصول على بيانات المنتجات ==========
@login_required
def get_product_data(request, product_id):
    """API للحصول على بيانات المنتج"""
    try:
        product = Product.objects.get(id=product_id)
        data = {
            'unit_price': float(product.unit_price),
            'stock_quantity': float(product.stock_quantity),
            'unit': product.unit,
        }
        return JsonResponse(data)
    except Product.DoesNotExist:
        return JsonResponse({'error': 'المنتج غير موجود'}, status=404)
