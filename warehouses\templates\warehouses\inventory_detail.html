{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if user_language == 'en' %}Inventory Details{% else %}تفاصيل المخزون{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .detail-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        box-shadow: 0 15px 50px rgba(31, 38, 135, 0.37);
    }

    .info-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 10px 40px rgba(31, 38, 135, 0.37);
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .info-card {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        border-left: 5px solid #667eea;
    }

    .stat-item {
        text-align: center;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 15px;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .status-normal { background: #d4edda; color: #155724; }
    .status-low { background: #fff3cd; color: #856404; }
    .status-out { background: #f8d7da; color: #721c24; }
    .status-high { background: #cce5ff; color: #004085; }

    .transaction-item {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        border-left: 4px solid #667eea;
        transition: all 0.3s ease;
    }

    .transaction-item:hover {
        transform: translateX(5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-action {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 0.5rem 1rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .progress-bar-custom {
        height: 8px;
        border-radius: 10px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .alert-warning-custom {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        border: none;
        border-radius: 15px;
        border-left: 5px solid #ffc107;
    }

    .alert-danger-custom {
        background: linear-gradient(135deg, #f8d7da, #fab1a0);
        border: none;
        border-radius: 15px;
        border-left: 5px solid #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<!-- Detail Header -->
<div class="detail-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-box-seam me-2"></i>
                    {{ inventory_item.product.name }}
                </h1>
                <p class="mb-0 opacity-75">
                    <strong>المخزن:</strong> {{ inventory_item.warehouse.name }} | 
                    <strong>الكود:</strong> {{ inventory_item.product.code }}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="btn-group">
                    <a href="{% url 'warehouses:inventory_list' %}" class="btn btn-light">
                        <i class="bi bi-arrow-left me-1"></i>قائمة المخزون
                    </a>
                    <a href="{% url 'warehouses:dashboard' %}" class="btn btn-light">
                        <i class="bi bi-house me-1"></i>لوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Stock Status Alert -->
    {% if inventory_item.is_out_of_stock %}
    <div class="alert alert-danger-custom">
        <h6><i class="bi bi-exclamation-triangle me-2"></i>تحذير: المخزون نافد!</h6>
        <p class="mb-0">هذا المنتج غير متوفر في المخزون. يجب إضافة مخزون فوراً.</p>
    </div>
    {% elif inventory_item.is_low_stock %}
    <div class="alert alert-warning-custom">
        <h6><i class="bi bi-exclamation-circle me-2"></i>تنبيه: المخزون منخفض!</h6>
        <p class="mb-0">الكمية الحالية أقل من الحد الأدنى المطلوب. يُنصح بإعادة التموين.</p>
    </div>
    {% endif %}

    <!-- Basic Information -->
    <div class="info-section">
        <h2 class="section-title">
            <i class="bi bi-info-circle"></i>
            المعلومات الأساسية
        </h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="info-card">
                    <h6><strong>معلومات المنتج</strong></h6>
                    <p><strong>الاسم:</strong> {{ inventory_item.product.name }}</p>
                    <p><strong>الكود:</strong> {{ inventory_item.product.code }}</p>
                    <p><strong>الفئة:</strong> {{ inventory_item.product.category.name|default:"غير محدد" }}</p>
                    <p><strong>وحدة القياس:</strong> {{ inventory_item.product.unit_of_measure|default:"قطعة" }}</p>
                </div>
            </div>
            <div class="col-md-6">
                <div class="info-card">
                    <h6><strong>معلومات المخزن</strong></h6>
                    <p><strong>المخزن:</strong> {{ inventory_item.warehouse.name }}</p>
                    <p><strong>كود المخزن:</strong> {{ inventory_item.warehouse.code }}</p>
                    <p><strong>الموقع:</strong> {{ inventory_item.location.name|default:"غير محدد" }}</p>
                    <p><strong>الحالة:</strong> 
                        <span class="status-badge status-{% if inventory_item.is_out_of_stock %}out{% elif inventory_item.is_low_stock %}low{% elif inventory_item.quantity_on_hand >= inventory_item.maximum_stock %}high{% else %}normal{% endif %}">
                            {{ inventory_item.stock_status }}
                        </span>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Stock Statistics -->
    <div class="info-section">
        <h2 class="section-title">
            <i class="bi bi-graph-up"></i>
            إحصائيات المخزون
        </h2>
        
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="stat-item">
                    <div class="stat-number">{{ inventory_item.quantity_on_hand }}</div>
                    <div class="stat-label">الكمية المتاحة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-item">
                    <div class="stat-number">{{ inventory_item.quantity_reserved }}</div>
                    <div class="stat-label">الكمية المحجوزة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-item">
                    <div class="stat-number">{{ inventory_item.available_quantity }}</div>
                    <div class="stat-label">المتاح للصرف</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-item">
                    <div class="stat-number">{{ inventory_item.total_value|floatformat:2 }}</div>
                    <div class="stat-label">إجمالي القيمة (ج.م)</div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-lg-3 col-md-6">
                <div class="stat-item">
                    <div class="stat-number">{{ inventory_item.minimum_stock }}</div>
                    <div class="stat-label">الحد الأدنى</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-item">
                    <div class="stat-number">{{ inventory_item.maximum_stock }}</div>
                    <div class="stat-label">الحد الأقصى</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-item">
                    <div class="stat-number text-primary">{{ inventory_item.product.cost_price|floatformat:2 }}</div>
                    <div class="stat-label">سعر التكلفة الحقيقي</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-item">
                    {% widthratio inventory_item.quantity_on_hand 1 inventory_item.product.cost_price as real_total_value %}
                    <div class="stat-number text-success">{{ real_total_value|floatformat:2 }}</div>
                    <div class="stat-label">إجمالي التكلفة الحقيقية</div>
                </div>
            </div>
        </div>

        <!-- Stock Level Progress -->
        <div class="mt-4">
            <h6>مستوى المخزون</h6>
            <div class="progress" style="height: 20px;">
                {% widthratio inventory_item.quantity_on_hand inventory_item.maximum_stock 100 as stock_percentage %}
                <div class="progress-bar progress-bar-custom" style="width: {{ stock_percentage }}%">
                    {{ stock_percentage }}%
                </div>
            </div>
            <div class="d-flex justify-content-between mt-1">
                <small>الحد الأدنى: {{ inventory_item.minimum_stock }}</small>
                <small>الحد الأقصى: {{ inventory_item.maximum_stock }}</small>
            </div>
        </div>
    </div>

    <!-- Consumption Analysis -->
    {% if coverage_days %}
    <div class="info-section">
        <h2 class="section-title">
            <i class="bi bi-speedometer2"></i>
            تحليل الاستهلاك
        </h2>
        
        <div class="row">
            <div class="col-md-4">
                <div class="stat-item">
                    <div class="stat-number">{{ consumption_rate|floatformat:1 }}</div>
                    <div class="stat-label">الاستهلاك (30 يوم)</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-item">
                    <div class="stat-number">{{ daily_consumption|floatformat:2 }}</div>
                    <div class="stat-label">المعدل اليومي</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-item">
                    <div class="stat-number">{{ coverage_days|floatformat:0 }}</div>
                    <div class="stat-label">أيام التغطية</div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="info-section">
        <h2 class="section-title">
            <i class="bi bi-lightning-charge"></i>
            إجراءات سريعة
        </h2>
        
        <div class="row">
            <div class="col-md-3">
                <a href="{% url 'warehouses:stock_receive' %}?product={{ inventory_item.product.id }}&warehouse={{ inventory_item.warehouse.id }}" class="btn btn-action w-100 mb-2">
                    <i class="bi bi-plus-circle me-2"></i>إضافة مخزون
                </a>
            </div>
            <div class="col-md-3">
                <a href="{% url 'warehouses:stock_issue' %}?product={{ inventory_item.product.id }}&warehouse={{ inventory_item.warehouse.id }}" class="btn btn-action w-100 mb-2">
                    <i class="bi bi-dash-circle me-2"></i>صرف مخزون
                </a>
            </div>
            <div class="col-md-3">
                <a href="{% url 'warehouses:transfer_stock' %}?product={{ inventory_item.product.id }}&from_warehouse={{ inventory_item.warehouse.id }}" class="btn btn-action w-100 mb-2">
                    <i class="bi bi-arrow-left-right me-2"></i>تحويل مخزون
                </a>
            </div>
            <div class="col-md-3">
                <a href="{% url 'warehouses:stock_adjustments' %}?product={{ inventory_item.product.id }}&warehouse={{ inventory_item.warehouse.id }}" class="btn btn-action w-100 mb-2">
                    <i class="bi bi-gear me-2"></i>تسوية مخزون
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    {% if recent_transactions %}
    <div class="info-section">
        <h2 class="section-title">
            <i class="bi bi-clock-history"></i>
            أحدث الحركات
        </h2>
        
        {% for transaction in recent_transactions %}
        <div class="transaction-item">
            <div class="row align-items-center">
                <div class="col-md-2">
                    <strong>{{ transaction.transaction_number }}</strong>
                </div>
                <div class="col-md-2">
                    <span class="badge bg-{% if transaction.transaction_type == 'receipt' %}success{% elif transaction.transaction_type == 'issue' %}danger{% else %}info{% endif %}">
                        {{ transaction.get_transaction_type_display }}
                    </span>
                </div>
                <div class="col-md-2">
                    <strong>{{ transaction.quantity }}</strong>
                </div>
                <div class="col-md-2">
                    {{ transaction.unit_cost|floatformat:2 }} ج.م
                </div>
                <div class="col-md-2">
                    {{ transaction.transaction_date|date:"Y-m-d H:i" }}
                </div>
                <div class="col-md-2">
                    {{ transaction.created_by.get_full_name|default:transaction.created_by.username }}
                </div>
            </div>
            {% if transaction.notes %}
            <div class="mt-2">
                <small class="text-muted">{{ transaction.notes }}</small>
            </div>
            {% endif %}
        </div>
        {% endfor %}
    </div>
    {% endif %}
</div>
{% endblock %}
