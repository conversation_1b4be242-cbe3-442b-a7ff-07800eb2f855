<!-- محتو<PERSON> Modal لتفاصيل القالب -->
<div class="modal-header">
    <h5 class="modal-title">
        <i class="bi bi-file-text me-2"></i>
        تفاصيل القالب: {{ template.name }}
    </h5>
    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
</div>

<div class="modal-body">
    <!-- معلومات أساسية -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="info-card">
                <h6 class="text-primary">
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات القالب
                </h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>الاسم:</strong></td>
                        <td>{{ template.name }}</td>
                    </tr>
                    <tr>
                        <td><strong>الوصف:</strong></td>
                        <td>{{ template.description|default:"لا يوجد وصف" }}</td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ الإنشاء:</strong></td>
                        <td>{{ template.created_at|date:"Y/m/d H:i" }}</td>
                    </tr>
                    <tr>
                        <td><strong>الحالة:</strong></td>
                        <td>
                            {% if template.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="info-card">
                <h6 class="text-success">
                    <i class="bi bi-bar-chart me-2"></i>
                    إحصائيات
                </h6>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">{{ total_permissions }}</div>
                        <div class="stat-label">إجمالي الصلاحيات</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ total_modules }}</div>
                        <div class="stat-label">عدد الوحدات</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ users_with_similar_permissions.count }}</div>
                        <div class="stat-label">المستخدمين المشابهين</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الصلاحيات حسب الوحدة -->
    <div class="permissions-section">
        <h6 class="text-warning mb-3">
            <i class="bi bi-shield-check me-2"></i>
            الصلاحيات المضمنة في القالب
        </h6>
        
        {% if permissions_by_module %}
            <div class="accordion" id="permissionsAccordion">
                {% for module, permissions in permissions_by_module.items %}
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" 
                                data-bs-toggle="collapse" 
                                data-bs-target="#collapse{{ forloop.counter }}"
                                aria-expanded="false">
                            <i class="bi bi-folder me-2"></i>
                            {{ module }} 
                            <span class="badge bg-primary ms-2">{{ permissions|length }}</span>
                        </button>
                    </h2>
                    <div id="collapse{{ forloop.counter }}" 
                         class="accordion-collapse collapse" 
                         data-bs-parent="#permissionsAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                {% for permission in permissions %}
                                <div class="col-md-6 mb-2">
                                    <div class="permission-item">
                                        <i class="bi bi-check-circle text-success me-2"></i>
                                        <strong>{{ permission.get_permission_type_display }}</strong>
                                        <br>
                                        <small class="text-muted">{{ permission.description|default:"لا يوجد وصف" }}</small>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                لا توجد صلاحيات مضافة لهذا القالب بعد.
            </div>
        {% endif %}
    </div>

    <!-- المستخدمين المشابهين -->
    {% if users_with_similar_permissions %}
    <div class="users-section mt-4">
        <h6 class="text-info mb-3">
            <i class="bi bi-people me-2"></i>
            مستخدمين لديهم صلاحيات مشابهة
        </h6>
        
        <div class="row">
            {% for user_profile in users_with_similar_permissions %}
            <div class="col-md-4 mb-2">
                <div class="user-card">
                    <div class="d-flex align-items-center">
                        <div class="user-avatar">
                            {% if user_profile.avatar %}
                                <img src="{{ user_profile.avatar.url }}" alt="{{ user_profile.user.get_full_name }}">
                            {% else %}
                                <i class="bi bi-person-circle"></i>
                            {% endif %}
                        </div>
                        <div class="user-info">
                            <div class="user-name">{{ user_profile.user.get_full_name|default:user_profile.user.username }}</div>
                            <div class="user-position">{{ user_profile.job_position.name|default:"غير محدد" }}</div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>

<div class="modal-footer">
    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
        <i class="bi bi-x-circle me-2"></i>
        إغلاق
    </button>
    <a href="{% url 'permissions:edit_permission_template' template.id %}" class="btn btn-warning">
        <i class="bi bi-pencil me-2"></i>
        تعديل القالب
    </a>
    <button type="button" class="btn btn-primary" onclick="applyTemplate({{ template.id }})">
        <i class="bi bi-person-plus me-2"></i>
        تطبيق على مستخدم
    </button>
    <a href="{% url 'permissions:template_details' template.id %}" class="btn btn-success">
        <i class="bi bi-eye me-2"></i>
        عرض التفاصيل
    </a>
</div>

<style>
.info-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    border-left: 4px solid var(--primary-color);
    height: 100%;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 0.5rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
}

.permission-item {
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 6px;
    border-left: 3px solid var(--success-color);
}

.user-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.user-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 0.75rem;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-avatar i {
    font-size: 1.5rem;
}

.user-name {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.user-position {
    font-size: 0.8rem;
    color: #6c757d;
}

.accordion-button {
    background: #f8f9fa;
    border: none;
}

.accordion-button:not(.collapsed) {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    color: var(--primary-color);
}
</style>
