from django.urls import path
from . import views
from django.http import HttpResponse

app_name = 'warehouses'

# دوال مؤقتة للصفحات المعطلة
def temp_view(request):
    from dashboard.context_processors import user_settings
    user_context = user_settings(request)
    user_language = user_context.get('user_language', 'ar')

    if user_language == 'en':
        message = "This page is under development - will be activated soon"
    else:
        message = "هذه الصفحة قيد التطوير - سيتم تفعيلها قريباً"

    return HttpResponse(message)

urlpatterns = [
    # لوحة التحكم الرئيسية
    path('', views.warehouses_dashboard, name='dashboard'),
    path('dashboard_advanced/', views.dashboard_advanced, name='dashboard_advanced'),

    # إدارة المخزون
    path('inventory/', views.inventory_list, name='inventory_list'),
    path('inventory/<int:item_id>/', views.inventory_detail, name='inventory_detail'),
    # الروابط القديمة محذوفة - استخدم النماذج الجديدة في receive_history و issue_history
    path('transfer/', views.transfer_stock, name='transfer_stock'),

    # التقارير والتنبيهات
    path('transactions/', temp_view, name='transactions_list'),
    path('alerts/', views.low_stock_alerts, name='low_stock_alerts'),
    path('adjustments/', views.stock_adjustments, name='stock_adjustments'),
    path('adjustments/history/', views.adjustments_history, name='adjustments_history'),
    path('count/', views.stock_count, name='stock_count'),
    path('count/history/', views.count_history, name='count_history'),
    path('transfer/history/', views.transfer_history, name='transfer_history'),
    path('issue/', views.stock_issue, name='stock_issue'),
    path('issue/history/', views.issue_history, name='issue_history'),
    path('receive/', views.stock_receive, name='stock_receive'),
    path('receive/history/', views.receive_history, name='receive_history'),
    path('api/notifications/', views.get_inventory_notifications, name='inventory_notifications'),
    path('reports/', views.stock_reports, name='stock_reports'),

    # AJAX APIs
    path('api/stock-info/', views.get_stock_info, name='get_stock_info'),

    # Debug endpoints (temporary)
    path('debug/product-costs/', views.debug_product_costs, name='debug_product_costs'),
    path('debug/manufacturing-orders/', views.debug_manufacturing_orders, name='debug_manufacturing_orders'),
]
