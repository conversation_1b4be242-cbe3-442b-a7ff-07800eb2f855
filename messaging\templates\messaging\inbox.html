<!DOCTYPE html>
<html>
<head>
    <title>صندوق الوارد - الرسائل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>

{% block breadcrumb %}الرسائل <i class="bi bi-chevron-left mx-2"></i> صندوق الوارد{% endblock %}

{% block extra_css %}
<style>
    .messaging-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .messaging-sidebar {
        background: #f8fafc;
        border-right: 1px solid #e2e8f0;
        padding: 1.5rem;
        min-height: 600px;
    }

    .messaging-content {
        padding: 1.5rem;
    }

    .message-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-bottom: 1px solid #f3f4f6;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        color: inherit;
    }

    .message-item:hover {
        background: #f8fafc;
        text-decoration: none;
        color: inherit;
    }

    .message-item.unread {
        background: #f0f9ff;
        border-left: 3px solid #3b82f6;
        font-weight: 600;
    }

    .message-avatar {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-left: 1rem;
    }

    .message-content {
        flex: 1;
    }

    .message-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .message-sender {
        font-weight: 600;
        color: #1f2937;
    }

    .message-time {
        font-size: 0.8rem;
        color: #6b7280;
    }

    .message-subject {
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.25rem;
    }

    .message-preview {
        font-size: 0.85rem;
        color: #6b7280;
        line-height: 1.4;
    }

    .message-actions {
        display: flex;
        gap: 0.5rem;
        margin-right: 1rem;
    }

    .message-action-btn {
        background: none;
        border: none;
        color: #6b7280;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .message-action-btn:hover {
        color: #374151;
        background: #f3f4f6;
    }

    .priority-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-left: 0.5rem;
    }

    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid #e2e8f0;
    }

    .stats-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #667eea;
    }

    .stats-label {
        font-size: 0.85rem;
        color: #6b7280;
    }

    .filter-btn {
        display: block;
        width: 100%;
        text-align: right;
        padding: 0.75rem 1rem;
        margin-bottom: 0.5rem;
        background: none;
        border: none;
        border-radius: 8px;
        color: #374151;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .filter-btn:hover, .filter-btn.active {
        background: #667eea;
        color: white;
        text-decoration: none;
    }

    .search-box {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .search-input {
        width: 100%;
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        font-size: 0.9rem;
    }

    .search-icon {
        position: absolute;
        left: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6b7280;
    }

    .compose-btn {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        margin-bottom: 1.5rem;
        width: 100%;
        justify-content: center;
    }

    .compose-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
        text-decoration: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="messaging-container">
                <div class="row g-0">
                    <!-- Sidebar -->
                    <div class="col-md-3">
                        <div class="messaging-sidebar">
                            <a href="{% url 'messaging:compose' %}" class="compose-btn">
                                <i class="bi bi-plus-circle"></i>
                                رسالة جديدة
                            </a>

                            <!-- إحصائيات -->
                            <div class="stats-card">
                                <div class="stats-number">{{ stats.total }}</div>
                                <div class="stats-label">إجمالي الرسائل</div>
                            </div>

                            <div class="stats-card">
                                <div class="stats-number">{{ stats.unread }}</div>
                                <div class="stats-label">غير مقروءة</div>
                            </div>

                            <div class="stats-card">
                                <div class="stats-number">{{ stats.starred }}</div>
                                <div class="stats-label">مميزة</div>
                            </div>

                            <!-- فلاتر -->
                            <div class="mt-4">
                                <h6 class="mb-3">تصفية الرسائل</h6>
                                <a href="{% url 'messaging:inbox' %}" class="filter-btn {% if not current_filter %}active{% endif %}">
                                    <i class="bi bi-inbox me-2"></i>
                                    جميع الرسائل
                                </a>
                                <a href="{% url 'messaging:inbox' %}?status=unread" class="filter-btn {% if current_filter == 'unread' %}active{% endif %}">
                                    <i class="bi bi-envelope me-2"></i>
                                    غير مقروءة
                                </a>
                                <a href="{% url 'messaging:inbox' %}?status=starred" class="filter-btn {% if current_filter == 'starred' %}active{% endif %}">
                                    <i class="bi bi-star me-2"></i>
                                    مميزة
                                </a>
                                <a href="{% url 'messaging:sent' %}" class="filter-btn">
                                    <i class="bi bi-send me-2"></i>
                                    المرسلة
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- المحتوى الرئيسي -->
                    <div class="col-md-9">
                        <div class="messaging-content">
                            <!-- شريط البحث -->
                            <div class="search-box">
                                <i class="bi bi-search search-icon"></i>
                                <input type="text" class="search-input" placeholder="البحث في الرسائل..." 
                                       value="{{ search_query }}" onkeyup="searchMessages(this.value)">
                            </div>

                            <!-- قائمة الرسائل -->
                            <div class="messages-list">
                                {% for message in messages %}
                                <a href="{% url 'messaging:view_message' message.id %}" 
                                   class="message-item {% if not message.is_read %}unread{% endif %}">
                                    <div class="message-avatar">
                                        {{ message.sender.first_name.0|default:message.sender.username.0|upper }}
                                    </div>
                                    <div class="message-content">
                                        <div class="message-header">
                                            <span class="message-sender">
                                                {{ message.sender.get_full_name|default:message.sender.username }}
                                            </span>
                                            <span class="message-time">{{ message.time_since_sent }}</span>
                                        </div>
                                        <div class="message-subject">{{ message.subject }}</div>
                                        <div class="message-preview">
                                            {{ message.content|truncatechars:100 }}
                                        </div>
                                    </div>
                                    <div class="message-actions">
                                        {% if message.is_starred %}
                                        <button class="message-action-btn" onclick="toggleStar({{ message.id }}, event)" title="إلغاء التمييز">
                                            <i class="bi bi-star-fill" style="color: #fbbf24;"></i>
                                        </button>
                                        {% else %}
                                        <button class="message-action-btn" onclick="toggleStar({{ message.id }}, event)" title="تمييز">
                                            <i class="bi bi-star"></i>
                                        </button>
                                        {% endif %}
                                        <div class="priority-indicator" style="background: {{ message.get_priority_color }};"></div>
                                    </div>
                                </a>
                                {% empty %}
                                <div class="text-center py-5">
                                    <i class="bi bi-inbox" style="font-size: 3rem; color: #d1d5db;"></i>
                                    <h5 class="mt-3 text-muted">لا توجد رسائل</h5>
                                    <p class="text-muted">صندوق الوارد فارغ</p>
                                </div>
                                {% endfor %}
                            </div>

                            <!-- التصفح -->
                            {% if messages.has_other_pages %}
                            <nav class="mt-4">
                                <ul class="pagination justify-content-center">
                                    {% if messages.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ messages.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_filter %}&status={{ current_filter }}{% endif %}">السابق</a>
                                    </li>
                                    {% endif %}
                                    
                                    {% for num in messages.paginator.page_range %}
                                    {% if num == messages.number %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_filter %}&status={{ current_filter }}{% endif %}">{{ num }}</a>
                                    </li>
                                    {% endif %}
                                    {% endfor %}
                                    
                                    {% if messages.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ messages.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_filter %}&status={{ current_filter }}{% endif %}">التالي</a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function searchMessages(query) {
    const url = new URL(window.location);
    if (query.trim()) {
        url.searchParams.set('search', query);
    } else {
        url.searchParams.delete('search');
    }
    window.location.href = url.toString();
}

function toggleStar(messageId, event) {
    event.preventDefault();
    event.stopPropagation();
    
    fetch(`/messages/message/${messageId}/star/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    });
}

// تحديث عدد الرسائل في الشريط العلوي
function updateMessageCount() {
    fetch('/messages/api/unread-count/')
    .then(response => response.json())
    .then(data => {
        const badge = document.getElementById('messageCount');
        if (badge) {
            badge.textContent = data.count;
            badge.style.display = data.count > 0 ? 'flex' : 'none';
        }
    });
}

// تحديث كل 30 ثانية
setInterval(updateMessageCount, 30000);
</script>

<div class="container mt-5">
    <h1>صندوق الوارد</h1>
    <p>مرحباً بك في نظام الرسائل</p>
    <a href="/messages/compose/" class="btn btn-primary">رسالة جديدة</a>

    <div class="mt-4">
        {% for message in messages %}
        <div class="card mb-3">
            <div class="card-body">
                <h5>{{ message.subject }}</h5>
                <p>من: {{ message.sender.get_full_name|default:message.sender.username }}</p>
                <p>{{ message.content|truncatechars:100 }}</p>
                <small class="text-muted">{{ message.time_since_sent }}</small>
            </div>
        </div>
        {% empty %}
        <p>لا توجد رسائل</p>
        {% endfor %}
    </div>
</div>

</body>
</html>
