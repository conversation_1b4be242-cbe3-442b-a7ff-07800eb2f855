from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Sum, Count, Q, F
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
from django.views.decorators.http import require_http_methods
from django.contrib.auth.models import User

from definitions.models import WarehouseDefinition, ProductDefinition, WarehouseLocation
from .models import (
    InventoryItem, InventoryTransaction, StockTransfer, StockTransferItem,
    StockCount, StockCountItem, StockAdjustment, StockAlert
)
from manufacturing.models import ManufacturingInventoryTransaction

# استيراد نظام الإشعارات
try:
    from notifications.views import create_notification
    NOTIFICATIONS_AVAILABLE = True
except ImportError:
    NOTIFICATIONS_AVAILABLE = False

try:
    from system_settings.utils import NotificationManager
    SYSTEM_NOTIFICATIONS_AVAILABLE = True
except ImportError:
    SYSTEM_NOTIFICATIONS_AVAILABLE = False


def create_inventory_notification(user, title, message, notification_type='inventory', priority='normal', action_url=None):
    """إنشاء إشعار تنبيه مخزون"""
    try:
        if NOTIFICATIONS_AVAILABLE:
            # استخدام نظام الإشعارات الجديد
            icon = 'bi-box'
            if 'نافد' in message:
                icon = 'bi-x-circle'
                priority = 'urgent'
            elif 'منخفض' in message:
                icon = 'bi-exclamation-triangle'
                priority = 'high'
            elif 'زائد' in message:
                icon = 'bi-arrow-up-circle'
                priority = 'normal'

            return create_notification(
                recipient=user,
                title=title,
                message=message,
                notification_type=notification_type,
                priority=priority,
                action_url=action_url,
                icon=icon
            )
        elif SYSTEM_NOTIFICATIONS_AVAILABLE:
            # استخدام نظام الإشعارات القديم
            return NotificationManager.add_notification(
                user=user,
                title=title,
                message=message,
                notification_type=notification_type,
                action_url=action_url or '',
                action_text='عرض التفاصيل'
            )
    except Exception as e:
        print(f"خطأ في إنشاء إشعار المخزون: {e}")
    return None


def check_and_create_inventory_alerts():
    """فحص المخزون وإنشاء التنبيهات والإشعارات"""
    try:
        # الحصول على جميع عناصر المخزون النشطة
        inventory_items = InventoryItem.objects.filter(is_active=True).select_related('product', 'warehouse')

        # المخزون النافد
        out_of_stock_items = inventory_items.filter(quantity_on_hand=0)

        # المخزون المنخفض
        low_stock_items = inventory_items.filter(
            quantity_on_hand__lte=F('minimum_stock'),
            quantity_on_hand__gt=0,
            minimum_stock__gt=0
        )

        # المخزون الزائد
        overstock_items = inventory_items.filter(
            quantity_on_hand__gte=F('maximum_stock'),
            maximum_stock__gt=0
        )

        # إنشاء إشعارات للمديرين والمسؤولين
        admin_users = User.objects.filter(
            Q(is_superuser=True) |
            Q(groups__name__icontains='مدير') |
            Q(groups__name__icontains='مخازن')
        ).distinct()

        # إشعارات المخزون النافد
        if out_of_stock_items.exists():
            count = out_of_stock_items.count()
            title = f"تنبيه: مخزون نافد ({count} منتج)"
            message = f"يوجد {count} منتج نفد من المخزون ويحتاج إلى إعادة تموين فوري."
            action_url = '/warehouses/alerts/?alert_type=out_of_stock'

            for user in admin_users:
                create_inventory_notification(
                    user=user,
                    title=title,
                    message=message,
                    priority='urgent',
                    action_url=action_url
                )

        # إشعارات المخزون المنخفض
        if low_stock_items.exists():
            count = low_stock_items.count()
            title = f"تنبيه: مخزون منخفض ({count} منتج)"
            message = f"يوجد {count} منتج وصل إلى الحد الأدنى ويحتاج إلى إعادة تموين."
            action_url = '/warehouses/alerts/?alert_type=low_stock'

            for user in admin_users:
                create_inventory_notification(
                    user=user,
                    title=title,
                    message=message,
                    priority='high',
                    action_url=action_url
                )

        # إشعارات المخزون الزائد
        if overstock_items.exists():
            count = overstock_items.count()
            title = f"تنبيه: مخزون زائد ({count} منتج)"
            message = f"يوجد {count} منتج تجاوز الحد الأقصى المسموح."
            action_url = '/warehouses/alerts/?alert_type=overstock'

            for user in admin_users:
                create_inventory_notification(
                    user=user,
                    title=title,
                    message=message,
                    priority='normal',
                    action_url=action_url
                )

        return {
            'out_of_stock': out_of_stock_items.count(),
            'low_stock': low_stock_items.count(),
            'overstock': overstock_items.count(),
            'notifications_sent': admin_users.count()
        }

    except Exception as e:
        print(f"خطأ في فحص تنبيهات المخزون: {e}")
        return None


@login_required
def warehouses_dashboard(request):
    """لوحة تحكم المخازن المتقدمة"""

    # فحص وإنشاء تنبيهات المخزون (كل 5 دقائق)
    from django.core.cache import cache
    last_check = cache.get('inventory_alerts_last_check')
    current_time = timezone.now()

    if not last_check or (current_time - last_check).total_seconds() > 300:  # 5 دقائق
        alert_results = check_and_create_inventory_alerts()
        cache.set('inventory_alerts_last_check', current_time, 300)  # cache لمدة 5 دقائق
        if alert_results:
            print(f"تم فحص التنبيهات: {alert_results}")

    # إحصائيات أساسية
    total_warehouses = WarehouseDefinition.objects.filter(is_active=True).count()
    total_products = ProductDefinition.objects.filter(is_active=True).count()
    total_inventory_items = InventoryItem.objects.filter(is_active=True).count()

    # إحصائيات المخزون (بناءً على إجمالي القيمة المحسوبة)
    total_stock_value = InventoryItem.objects.aggregate(
        total=Sum('total_value')
    )['total'] or 0

    # المخزون المنخفض
    low_stock_items = InventoryItem.objects.filter(
        quantity_on_hand__lte=F('minimum_stock'),
        is_active=True
    ).count()

    # المخزون النافد
    out_of_stock_items = InventoryItem.objects.filter(
        quantity_on_hand__lte=0,
        is_active=True
    ).count()

    # التنبيهات النشطة
    active_alerts = StockAlert.objects.filter(
        is_active=True,
        is_acknowledged=False
    ).count()

    # حركات اليوم
    today = timezone.now().date()
    today_transactions = InventoryTransaction.objects.filter(
        transaction_date__date=today
    ).count()

    # التحويلات المعلقة
    pending_transfers = StockTransfer.objects.filter(
        status__in=['pending', 'in_transit']
    ).count()

    # أحدث الحركات
    recent_transactions = InventoryTransaction.objects.select_related(
        'inventory_item__warehouse',
        'inventory_item__product',
        'created_by'
    ).order_by('-created_at')[:10]

    # المخازن النشطة مع إحصائيات متقدمة (بناءً على إجمالي القيمة المحسوبة)
    warehouses = WarehouseDefinition.objects.filter(is_active=True).annotate(
        items_count=Count('inventoryitem'),
        warehouse_total_value=Sum('inventoryitem__total_value'),
        low_stock_count=Count('inventoryitem', filter=Q(inventoryitem__quantity_on_hand__lte=F('inventoryitem__minimum_stock'))),
        out_of_stock_count=Count('inventoryitem', filter=Q(inventoryitem__quantity_on_hand__lte=0)),
        total_quantity=Sum('inventoryitem__quantity_on_hand')
    )[:10]

    # أهم المنتجات (حسب القيمة الحقيقية)
    top_products = InventoryItem.objects.select_related('product').annotate(
        calculated_value=F('quantity_on_hand') * F('product__cost_price')
    ).filter(is_active=True).order_by('-calculated_value')[:10]

    # إحصائيات الأسبوع الماضي
    week_ago = timezone.now() - timedelta(days=7)
    week_transactions = InventoryTransaction.objects.filter(
        transaction_date__gte=week_ago
    ).values('transaction_type').annotate(
        count=Count('id'),
        total_quantity=Sum('quantity')
    )

    context = {
        # إحصائيات عامة
        'total_warehouses': total_warehouses,
        'total_products': total_products,
        'total_inventory_items': total_inventory_items,
        'total_stock_value': total_stock_value,

        # تنبيهات
        'low_stock_items': low_stock_items,
        'out_of_stock_items': out_of_stock_items,
        'active_alerts': active_alerts,

        # حركات
        'today_transactions': today_transactions,
        'pending_transfers': pending_transfers,
        'recent_transactions': recent_transactions,
        'week_transactions': week_transactions,

        # بيانات
        'warehouses': warehouses,
        'top_products': top_products,

        # معلومات الصفحة
        'page_title': 'إدارة المخازن',
        'page_title_en': 'Warehouse Management',
    }

    return render(request, 'warehouses/dashboard_advanced.html', context)


@login_required
def dashboard_advanced(request):
    """لوحة تحكم المخازن المتقدمة - نسخة بديلة"""
    # استخدام نفس منطق warehouses_dashboard
    return warehouses_dashboard(request)


# ===== إدارة المخزون =====
@login_required
def inventory_list(request):
    """قائمة المخزون"""
    warehouse_id = request.GET.get('warehouse')
    search_query = request.GET.get('search', '')
    stock_status = request.GET.get('status', '')

    # البحث والفلترة
    inventory_items = InventoryItem.objects.select_related(
        'warehouse', 'product', 'location'
    ).filter(is_active=True)

    if warehouse_id:
        inventory_items = inventory_items.filter(warehouse_id=warehouse_id)

    if search_query:
        inventory_items = inventory_items.filter(
            Q(product__name__icontains=search_query) |
            Q(product__code__icontains=search_query)
        )

    if stock_status == 'low':
        inventory_items = inventory_items.filter(quantity_on_hand__lte=F('minimum_stock'))
    elif stock_status == 'out':
        inventory_items = inventory_items.filter(quantity_on_hand__lte=0)
    elif stock_status == 'normal':
        inventory_items = inventory_items.filter(
            quantity_on_hand__gt=F('minimum_stock'),
            quantity_on_hand__lt=F('maximum_stock')
        )

    # ترتيب النتائج
    inventory_items = inventory_items.order_by('warehouse__name', 'product__name')

    # المخازن للفلترة
    warehouses = WarehouseDefinition.objects.filter(is_active=True)

    context = {
        'inventory_items': inventory_items,
        'warehouses': warehouses,
        'selected_warehouse': warehouse_id,
        'search_query': search_query,
        'stock_status': stock_status,
        'page_title': 'إدارة المخزون',
    }

    return render(request, 'warehouses/inventory_list.html', context)


# دالة add_stock محذوفة - استخدم stock_receive بدلاً منها


@login_required
def inventory_detail(request, item_id):
    """تفاصيل عنصر المخزون"""
    inventory_item = get_object_or_404(
        InventoryItem.objects.select_related('warehouse', 'product', 'location'),
        id=item_id,
        is_active=True
    )

    # أحدث الحركات لهذا العنصر
    recent_transactions = InventoryTransaction.objects.filter(
        inventory_item=inventory_item
    ).select_related('created_by').order_by('-transaction_date')[:20]

    # إحصائيات الحركات
    transactions_stats = InventoryTransaction.objects.filter(
        inventory_item=inventory_item
    ).values('transaction_type').annotate(
        count=Count('id'),
        total_quantity=Sum('quantity')
    )

    # حساب معدل الاستهلاك (آخر 30 يوم)
    thirty_days_ago = timezone.now() - timedelta(days=30)
    consumption_rate = InventoryTransaction.objects.filter(
        inventory_item=inventory_item,
        transaction_type__in=['issue', 'transfer_out'],
        transaction_date__gte=thirty_days_ago
    ).aggregate(
        total_consumed=Sum('quantity')
    )['total_consumed'] or 0

    # تقدير أيام التغطية
    daily_consumption = consumption_rate / 30 if consumption_rate > 0 else 0
    coverage_days = inventory_item.quantity_on_hand / daily_consumption if daily_consumption > 0 else float('inf')

    context = {
        'inventory_item': inventory_item,
        'recent_transactions': recent_transactions,
        'transactions_stats': transactions_stats,
        'consumption_rate': consumption_rate,
        'daily_consumption': daily_consumption,
        'coverage_days': coverage_days if coverage_days != float('inf') else None,
        'page_title': f'تفاصيل المخزون - {inventory_item.product.name}',
    }

    return render(request, 'warehouses/inventory_detail.html', context)


# دالة remove_stock محذوفة - استخدم stock_issue بدلاً منها


@login_required
def transfer_stock(request):
    """تحويل مخزون بين المخازن"""
    print(f"DEBUG TRANSFER: Request method = {request.method}")
    if request.method == 'POST':
        print("DEBUG TRANSFER: POST request received")
        try:
            from_warehouse_id = request.POST.get('from_warehouse')
            to_warehouse_id = request.POST.get('to_warehouse')
            product_id = request.POST.get('product')
            quantity = Decimal(request.POST.get('quantity', 0))
            notes = request.POST.get('notes', '')

            # التحقق من صحة البيانات
            if not from_warehouse_id or not to_warehouse_id or not product_id or quantity <= 0:
                messages.error(request, 'يرجى ملء جميع الحقول المطلوبة بشكل صحيح.')
                return redirect('warehouses:transfer_stock')

            if from_warehouse_id == to_warehouse_id:
                messages.error(request, 'لا يمكن التحويل إلى نفس المخزن.')
                return redirect('warehouses:transfer_stock')

            from_warehouse = get_object_or_404(WarehouseDefinition, id=from_warehouse_id)
            to_warehouse = get_object_or_404(WarehouseDefinition, id=to_warehouse_id)
            product = get_object_or_404(ProductDefinition, id=product_id)

            # البحث عن عنصر المخزون في المخزن المصدر
            try:
                from_inventory = InventoryItem.objects.get(
                    warehouse=from_warehouse,
                    product=product,
                    is_active=True
                )
            except InventoryItem.DoesNotExist:
                messages.error(request, f'المنتج {product.name} غير موجود في مخزن {from_warehouse.name}.')
                return redirect('warehouses:transfer_stock')

            # التحقق من توفر الكمية
            if from_inventory.available_quantity < quantity:
                messages.error(request, f'الكمية المتاحة ({from_inventory.available_quantity}) أقل من الكمية المطلوبة ({quantity}).')
                return redirect('warehouses:transfer_stock')

            # البحث عن أو إنشاء عنصر المخزون في المخزن المستهدف
            to_inventory, created = InventoryItem.objects.get_or_create(
                warehouse=to_warehouse,
                product=product,
                defaults={
                    'quantity_on_hand': Decimal('0'),
                    'average_cost': from_inventory.average_cost,
                    'last_cost': from_inventory.average_cost,
                    'minimum_stock': product.minimum_stock or Decimal('0'),
                    'maximum_stock': product.maximum_stock or Decimal('1000'),
                    'total_value': Decimal('0'),
                    'is_active': True
                }
            )

            # حفظ الأرصدة قبل التحويل
            from_balance_before = from_inventory.quantity_on_hand
            to_balance_before = to_inventory.quantity_on_hand

            # تحديث المخزن المصدر (خصم)
            from_inventory.quantity_on_hand -= quantity
            from_inventory.total_value = from_inventory.quantity_on_hand * from_inventory.average_cost
            from_inventory.last_issued_date = timezone.now()
            from_inventory.save()

            # تحديث المخزن المستهدف (إضافة)
            old_total_value = to_inventory.quantity_on_hand * to_inventory.average_cost
            new_total_value = old_total_value + (quantity * from_inventory.average_cost)
            to_inventory.quantity_on_hand += quantity

            # حساب متوسط التكلفة الجديد
            if to_inventory.quantity_on_hand > 0:
                to_inventory.average_cost = new_total_value / to_inventory.quantity_on_hand

            to_inventory.total_value = new_total_value
            to_inventory.last_received_date = timezone.now()
            to_inventory.save()

            # إنشاء حركات المخزون
            transfer_number = f"TRF-{timezone.now().strftime('%Y%m%d%H%M%S')}"

            # حركة الصرف من المخزن المصدر
            InventoryTransaction.objects.create(
                transaction_number=f"{transfer_number}-OUT",
                transaction_type='transfer_out',
                inventory_item=from_inventory,
                quantity=quantity,
                unit_cost=from_inventory.average_cost,
                total_cost=quantity * from_inventory.average_cost,
                balance_before=from_balance_before,
                balance_after=from_inventory.quantity_on_hand,
                reference_number=transfer_number,
                notes=f"تحويل إلى {to_warehouse.name}. {notes}",
                created_by=request.user,
                is_approved=True,
                approved_by=request.user,
                approved_at=timezone.now()
            )

            # حركة الإضافة للمخزن المستهدف
            InventoryTransaction.objects.create(
                transaction_number=f"{transfer_number}-IN",
                transaction_type='transfer_in',
                inventory_item=to_inventory,
                quantity=quantity,
                unit_cost=from_inventory.average_cost,
                total_cost=quantity * from_inventory.average_cost,
                balance_before=to_balance_before,
                balance_after=to_inventory.quantity_on_hand,
                reference_number=transfer_number,
                notes=f"تحويل من {from_warehouse.name}. {notes}",
                created_by=request.user,
                is_approved=True,
                approved_by=request.user,
                approved_at=timezone.now()
            )

            messages.success(request, f'تم تحويل {quantity} من {product.name} من {from_warehouse.name} إلى {to_warehouse.name} بنجاح!')
            return redirect('warehouses:inventory_list')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحويل المخزون: {str(e)}')

    # GET request
    warehouses = WarehouseDefinition.objects.filter(is_active=True)
    products = ProductDefinition.objects.filter(is_active=True)

    # الحصول على المعاملات من URL
    selected_product = request.GET.get('product')
    selected_from_warehouse = request.GET.get('from_warehouse')

    context = {
        'warehouses': warehouses,
        'products': products,
        'selected_product': selected_product,
        'selected_from_warehouse': selected_from_warehouse,
        'page_title': 'تحويل مخزون',
    }

    return render(request, 'warehouses/transfer_stock.html', context)


@login_required
def stock_count(request):
    """جرد المخزون"""
    print(f"DEBUG: Request method = {request.method}")
    if request.method == 'POST':
        print("DEBUG: POST request received")
        try:
            warehouse_id = request.POST.get('warehouse')
            product_id = request.POST.get('product')
            counted_quantity = Decimal(request.POST.get('counted_quantity', 0))
            notes = request.POST.get('notes', '')

            print(f"DEBUG: warehouse_id={warehouse_id}, product_id={product_id}, quantity={counted_quantity}")

            if not warehouse_id or not product_id:
                messages.error(request, 'يرجى اختيار المخزن والمنتج.')
                return redirect('warehouses:stock_count')

            warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)
            product = get_object_or_404(ProductDefinition, id=product_id)

            # البحث عن المخزون أو إنشاؤه
            inventory_item, created = InventoryItem.objects.get_or_create(
            warehouse=warehouse,
            product=product,
            defaults={
                'quantity_on_hand': Decimal('0'),
                'minimum_stock': product.minimum_stock or Decimal('0'),
                'maximum_stock': product.maximum_stock or Decimal('1000'),
                'average_cost': product.cost_price or Decimal('0'),
                'last_cost': product.cost_price or Decimal('0'),
                'total_value': Decimal('0'),
                'is_active': True
            }
            )

            # حساب الفرق
            old_quantity = inventory_item.quantity_on_hand
            difference = counted_quantity - old_quantity

            # تحديث المخزون
            inventory_item.quantity_on_hand = counted_quantity
            inventory_item.total_value = counted_quantity * product.cost_price
            inventory_item.last_counted_date = timezone.now()
            inventory_item.save()

            # إنشاء حركة الجرد
            if difference != 0:
                InventoryTransaction.objects.create(
                    transaction_number=f"CNT-{timezone.now().strftime('%Y%m%d%H%M%S')}",
                    transaction_type='count_adjustment',
                    inventory_item=inventory_item,
                    quantity=abs(difference),
                    unit_cost=product.cost_price,
                    total_cost=abs(difference) * product.cost_price,
                    balance_before=old_quantity,
                    balance_after=counted_quantity,
                    reference_number=f"جرد-{timezone.now().strftime('%Y%m%d')}",
                    notes=f"جرد مخزون. {notes}",
                    created_by=request.user,
                    is_approved=True,
                    approved_by=request.user,
                    approved_at=timezone.now()
                )

            # رسالة النجاح
            if difference > 0:
                messages.success(request, f'تم الجرد! زيادة: {difference:.3f}')
            elif difference < 0:
                messages.success(request, f'تم الجرد! نقص: {abs(difference):.3f}')
            else:
                messages.success(request, 'تم الجرد! لا يوجد فرق')

            return redirect('warehouses:inventory_list')

        except Exception as e:
            print(f"ERROR in stock_count: {str(e)}")
            messages.error(request, f'حدث خطأ: {str(e)}')
            return redirect('warehouses:stock_count')

    # عرض النموذج
    context = {
        'warehouses': WarehouseDefinition.objects.filter(is_active=True),
        'products': ProductDefinition.objects.filter(is_active=True),
        'page_title': 'جرد مبسط',
    }

    return render(request, 'warehouses/stock_count.html', context)


@login_required
def count_history(request):
    """سجل عمليات الجرد"""
    # الفلاتر
    warehouse_id = request.GET.get('warehouse')
    product_id = request.GET.get('product')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    search_query = request.GET.get('search', '')

    # البحث والفلترة
    count_transactions = InventoryTransaction.objects.select_related(
        'inventory_item__warehouse', 'inventory_item__product', 'created_by'
    ).filter(transaction_type='count_adjustment').order_by('-created_at')

    if warehouse_id:
        count_transactions = count_transactions.filter(inventory_item__warehouse_id=warehouse_id)

    if product_id:
        count_transactions = count_transactions.filter(inventory_item__product_id=product_id)

    if date_from:
        count_transactions = count_transactions.filter(created_at__date__gte=date_from)

    if date_to:
        count_transactions = count_transactions.filter(created_at__date__lte=date_to)

    if search_query:
        count_transactions = count_transactions.filter(
            Q(inventory_item__product__name__icontains=search_query) |
            Q(inventory_item__product__code__icontains=search_query) |
            Q(inventory_item__warehouse__name__icontains=search_query) |
            Q(notes__icontains=search_query)
        )

    # البيانات للفلاتر
    warehouses = WarehouseDefinition.objects.filter(is_active=True)
    products = ProductDefinition.objects.filter(is_active=True)

    # إحصائيات
    total_counts = count_transactions.count()

    context = {
        'count_transactions': count_transactions,
        'warehouses': warehouses,
        'products': products,
        'selected_warehouse': warehouse_id,
        'selected_product': product_id,
        'date_from': date_from,
        'date_to': date_to,
        'search_query': search_query,
        'total_counts': total_counts,
        'page_title': 'سجل عمليات الجرد',
    }

    return render(request, 'warehouses/count_history.html', context)


@login_required
def transfer_history(request):
    """سجل عمليات تحويل المخزون"""
    # الفلاتر
    from_warehouse_id = request.GET.get('from_warehouse')
    to_warehouse_id = request.GET.get('to_warehouse')
    product_id = request.GET.get('product')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    search_query = request.GET.get('search', '')

    # البحث والفلترة
    transfer_transactions = InventoryTransaction.objects.select_related(
        'inventory_item__warehouse', 'inventory_item__product', 'created_by'
    ).filter(transaction_type='transfer_out').order_by('-created_at')

    if from_warehouse_id:
        transfer_transactions = transfer_transactions.filter(inventory_item__warehouse_id=from_warehouse_id)

    if to_warehouse_id:
        # البحث في الملاحظات عن المخزن المستقبل
        transfer_transactions = transfer_transactions.filter(notes__icontains=f"إلى مخزن")

    if product_id:
        transfer_transactions = transfer_transactions.filter(inventory_item__product_id=product_id)

    if date_from:
        transfer_transactions = transfer_transactions.filter(created_at__date__gte=date_from)

    if date_to:
        transfer_transactions = transfer_transactions.filter(created_at__date__lte=date_to)

    if search_query:
        transfer_transactions = transfer_transactions.filter(
            Q(inventory_item__product__name__icontains=search_query) |
            Q(inventory_item__product__code__icontains=search_query) |
            Q(inventory_item__warehouse__name__icontains=search_query) |
            Q(notes__icontains=search_query) |
            Q(reference_number__icontains=search_query)
        )

    # البيانات للفلاتر
    warehouses = WarehouseDefinition.objects.filter(is_active=True)
    products = ProductDefinition.objects.filter(is_active=True)

    # إحصائيات
    total_transfers = transfer_transactions.count()
    total_quantity = transfer_transactions.aggregate(
        total=Sum('quantity')
    )['total'] or 0
    total_value = transfer_transactions.aggregate(
        total=Sum('total_cost')
    )['total'] or 0

    context = {
        'transfer_transactions': transfer_transactions,
        'warehouses': warehouses,
        'products': products,
        'selected_from_warehouse': from_warehouse_id,
        'selected_to_warehouse': to_warehouse_id,
        'selected_product': product_id,
        'date_from': date_from,
        'date_to': date_to,
        'search_query': search_query,
        'total_transfers': total_transfers,
        'total_quantity': total_quantity,
        'total_value': total_value,
        'page_title': 'سجل عمليات تحويل المخزون',
    }

    return render(request, 'warehouses/transfer_history.html', context)


@login_required
def stock_issue(request):
    """صرف مخزون"""
    print(f"DEBUG ISSUE: Request method = {request.method}")
    if request.method == 'POST':
        print("DEBUG ISSUE: POST request received")
        try:
            warehouse_id = request.POST.get('warehouse')
            product_id = request.POST.get('product')
            issued_quantity = Decimal(request.POST.get('issued_quantity', 0))
            reason = request.POST.get('reason', '')
            notes = request.POST.get('notes', '')

            print(f"DEBUG ISSUE: warehouse_id={warehouse_id}, product_id={product_id}, quantity={issued_quantity}")

            if not warehouse_id or not product_id or issued_quantity <= 0:
                messages.error(request, 'يرجى ملء جميع الحقول المطلوبة.')
                return redirect('warehouses:stock_issue')

            warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)
            product = get_object_or_404(ProductDefinition, id=product_id)

            # البحث عن المخزون
            try:
                inventory_item = InventoryItem.objects.get(
                    warehouse=warehouse,
                    product=product,
                    is_active=True
                )
            except InventoryItem.DoesNotExist:
                messages.error(request, f'المنتج {product.name} غير موجود في مخزن {warehouse.name}.')
                return redirect('warehouses:stock_issue')

            # التحقق من توفر الكمية
            if inventory_item.quantity_on_hand < issued_quantity:
                messages.error(request, f'الكمية المطلوبة ({issued_quantity}) أكبر من المتوفر ({inventory_item.quantity_on_hand}).')
                return redirect('warehouses:stock_issue')

            # التحقق من المخزون المنخفض بعد الصرف
            remaining_quantity = inventory_item.quantity_on_hand - issued_quantity
            if remaining_quantity <= inventory_item.minimum_stock and inventory_item.minimum_stock > 0:
                # إنشاء إشعار فوري للمخزون المنخفض
                admin_users = User.objects.filter(
                    Q(is_superuser=True) |
                    Q(groups__name__icontains='مدير') |
                    Q(groups__name__icontains='مخازن')
                ).distinct()

                for user in admin_users:
                    create_inventory_notification(
                        user=user,
                        title=f"تنبيه: مخزون منخفض - {product.name}",
                        message=f"المنتج {product.name} في مخزن {warehouse.name} وصل إلى الحد الأدنى ({remaining_quantity:.1f})",
                        priority='high',
                        action_url=f'/warehouses/alerts/?product={product.id}'
                    )

            # حفظ الرصيد قبل التعديل
            old_quantity = inventory_item.quantity_on_hand

            # تحديث المخزون
            inventory_item.quantity_on_hand -= issued_quantity
            inventory_item.total_value = inventory_item.quantity_on_hand * product.cost_price
            inventory_item.save()

            # إنشاء حركة الصرف
            InventoryTransaction.objects.create(
                transaction_number=f"ISS-{timezone.now().strftime('%Y%m%d%H%M%S')}",
                transaction_type='issue',
                inventory_item=inventory_item,
                quantity=issued_quantity,
                unit_cost=product.cost_price,
                total_cost=issued_quantity * product.cost_price,
                balance_before=old_quantity,
                balance_after=inventory_item.quantity_on_hand,
                reference_number=f"صرف-{timezone.now().strftime('%Y%m%d')}",
                notes=f"صرف مخزون - السبب: {reason}. {notes}",
                created_by=request.user,
                is_approved=True,
                approved_by=request.user,
                approved_at=timezone.now()
            )

            messages.success(request, f'تم صرف {issued_quantity:.3f} من {product.name} بنجاح!')

            # فحص التنبيهات بعد الصرف
            check_and_create_inventory_alerts()

            return redirect('warehouses:issue_history')

        except Exception as e:
            print(f"ERROR in stock_issue: {str(e)}")
            messages.error(request, f'حدث خطأ: {str(e)}')
            return redirect('warehouses:stock_issue')

    # عرض النموذج
    context = {
        'warehouses': WarehouseDefinition.objects.filter(is_active=True),
        'products': ProductDefinition.objects.filter(is_active=True),
        'current_datetime': timezone.now().strftime('%Y-%m-%dT%H:%M'),
        'page_title': 'صرف مخزون',
    }
    return render(request, 'warehouses/stock_issue.html', context)


@login_required
def issue_history(request):
    """سجل عمليات صرف المخزون"""
    # الفلاتر
    warehouse_id = request.GET.get('warehouse')
    product_id = request.GET.get('product')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    search_query = request.GET.get('search', '')

    # البحث والفلترة
    issue_transactions = InventoryTransaction.objects.select_related(
        'inventory_item__warehouse', 'inventory_item__product', 'created_by'
    ).filter(transaction_type='issue').order_by('-created_at')

    if warehouse_id:
        issue_transactions = issue_transactions.filter(inventory_item__warehouse_id=warehouse_id)

    if product_id:
        issue_transactions = issue_transactions.filter(inventory_item__product_id=product_id)

    if date_from:
        issue_transactions = issue_transactions.filter(created_at__date__gte=date_from)

    if date_to:
        issue_transactions = issue_transactions.filter(created_at__date__lte=date_to)

    if search_query:
        issue_transactions = issue_transactions.filter(
            Q(inventory_item__product__name__icontains=search_query) |
            Q(inventory_item__product__code__icontains=search_query) |
            Q(inventory_item__warehouse__name__icontains=search_query) |
            Q(notes__icontains=search_query) |
            Q(reference_number__icontains=search_query)
        )

    # البيانات للفلاتر
    warehouses = WarehouseDefinition.objects.filter(is_active=True)
    products = ProductDefinition.objects.filter(is_active=True)

    # إحصائيات
    total_issues = issue_transactions.count()
    total_quantity = issue_transactions.aggregate(
        total=Sum('quantity')
    )['total'] or 0
    total_value = issue_transactions.aggregate(
        total=Sum('total_cost')
    )['total'] or 0

    context = {
        'issue_transactions': issue_transactions,
        'warehouses': warehouses,
        'products': products,
        'selected_warehouse': warehouse_id,
        'selected_product': product_id,
        'date_from': date_from,
        'date_to': date_to,
        'search_query': search_query,
        'total_issues': total_issues,
        'total_quantity': total_quantity,
        'total_value': total_value,
        'page_title': 'سجل عمليات صرف المخزون',
    }

    return render(request, 'warehouses/issue_history.html', context)


@login_required
def stock_receive(request):
    """إضافة مخزون"""
    print(f"DEBUG RECEIVE: Request method = {request.method}")
    if request.method == 'POST':
        print("DEBUG RECEIVE: POST request received")
        try:
            warehouse_id = request.POST.get('warehouse')
            product_id = request.POST.get('product')
            received_quantity = Decimal(request.POST.get('received_quantity', 0))
            unit_cost = Decimal(request.POST.get('unit_cost', 0))
            reason = request.POST.get('reason', '')
            notes = request.POST.get('notes', '')

            print(f"DEBUG RECEIVE: warehouse_id={warehouse_id}, product_id={product_id}, quantity={received_quantity}")

            if not warehouse_id or not product_id or received_quantity <= 0:
                messages.error(request, 'يرجى ملء جميع الحقول المطلوبة.')
                return redirect('warehouses:stock_receive')

            warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)
            product = get_object_or_404(ProductDefinition, id=product_id)

            # البحث عن المخزون أو إنشاؤه
            inventory_item, created = InventoryItem.objects.get_or_create(
                warehouse=warehouse,
                product=product,
                defaults={
                    'quantity_on_hand': Decimal('0'),
                    'minimum_stock': product.minimum_stock or Decimal('0'),
                    'maximum_stock': product.maximum_stock or Decimal('1000'),
                    'average_cost': unit_cost or product.cost_price or Decimal('0'),
                    'last_cost': unit_cost or product.cost_price or Decimal('0'),
                    'total_value': Decimal('0'),
                    'is_active': True
                }
            )

            # حفظ الرصيد قبل التعديل
            old_quantity = inventory_item.quantity_on_hand

            # تحديث المخزون
            inventory_item.quantity_on_hand += received_quantity

            # تحديث التكلفة المتوسطة
            if unit_cost > 0:
                total_cost = (inventory_item.quantity_on_hand - received_quantity) * inventory_item.average_cost + received_quantity * unit_cost
                inventory_item.average_cost = total_cost / inventory_item.quantity_on_hand if inventory_item.quantity_on_hand > 0 else unit_cost
                inventory_item.last_cost = unit_cost

            inventory_item.total_value = inventory_item.quantity_on_hand * inventory_item.average_cost
            inventory_item.save()

            # إنشاء حركة الإضافة
            InventoryTransaction.objects.create(
                transaction_number=f"REC-{timezone.now().strftime('%Y%m%d%H%M%S')}",
                transaction_type='receive',
                inventory_item=inventory_item,
                quantity=received_quantity,
                unit_cost=unit_cost or product.cost_price,
                total_cost=received_quantity * (unit_cost or product.cost_price),
                balance_before=old_quantity,
                balance_after=inventory_item.quantity_on_hand,
                reference_number=f"إضافة-{timezone.now().strftime('%Y%m%d')}",
                notes=f"إضافة مخزون - السبب: {reason}. {notes}",
                created_by=request.user,
                is_approved=True,
                approved_by=request.user,
                approved_at=timezone.now()
            )

            messages.success(request, f'تم إضافة {received_quantity:.3f} من {product.name} بنجاح!')

            # فحص التنبيهات بعد الإضافة
            check_and_create_inventory_alerts()

            return redirect('warehouses:receive_history')

        except Exception as e:
            print(f"ERROR in stock_receive: {str(e)}")
            messages.error(request, f'حدث خطأ: {str(e)}')
            return redirect('warehouses:stock_receive')

    # عرض النموذج
    context = {
        'warehouses': WarehouseDefinition.objects.filter(is_active=True),
        'products': ProductDefinition.objects.filter(is_active=True),
        'current_datetime': timezone.now().strftime('%Y-%m-%dT%H:%M'),
        'page_title': 'إضافة مخزون',
    }
    return render(request, 'warehouses/stock_receive.html', context)


@login_required
def receive_history(request):
    """سجل عمليات إضافة المخزون"""
    # الفلاتر
    warehouse_id = request.GET.get('warehouse')
    product_id = request.GET.get('product')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    search_query = request.GET.get('search', '')

    # البحث والفلترة
    receive_transactions = InventoryTransaction.objects.select_related(
        'inventory_item__warehouse', 'inventory_item__product', 'created_by'
    ).filter(transaction_type='receive').order_by('-created_at')

    if warehouse_id:
        receive_transactions = receive_transactions.filter(inventory_item__warehouse_id=warehouse_id)

    if product_id:
        receive_transactions = receive_transactions.filter(inventory_item__product_id=product_id)

    if date_from:
        receive_transactions = receive_transactions.filter(created_at__date__gte=date_from)

    if date_to:
        receive_transactions = receive_transactions.filter(created_at__date__lte=date_to)

    if search_query:
        receive_transactions = receive_transactions.filter(
            Q(inventory_item__product__name__icontains=search_query) |
            Q(inventory_item__product__code__icontains=search_query) |
            Q(inventory_item__warehouse__name__icontains=search_query) |
            Q(notes__icontains=search_query) |
            Q(reference_number__icontains=search_query)
        )

    # البيانات للفلاتر
    warehouses = WarehouseDefinition.objects.filter(is_active=True)
    products = ProductDefinition.objects.filter(is_active=True)

    # إحصائيات
    total_receives = receive_transactions.count()
    total_quantity = receive_transactions.aggregate(
        total=Sum('quantity')
    )['total'] or 0
    total_value = receive_transactions.aggregate(
        total=Sum('total_cost')
    )['total'] or 0

    context = {
        'receive_transactions': receive_transactions,
        'warehouses': warehouses,
        'products': products,
        'selected_warehouse': warehouse_id,
        'selected_product': product_id,
        'date_from': date_from,
        'date_to': date_to,
        'search_query': search_query,
        'total_receives': total_receives,
        'total_quantity': total_quantity,
        'total_value': total_value,
        'page_title': 'سجل عمليات إضافة المخزون',
    }

    return render(request, 'warehouses/receive_history.html', context)


@login_required
def get_inventory_notifications(request):
    """API لجلب إشعارات المخزون للشريط العلوي"""
    try:
        # فحص التنبيهات الحالية
        alert_results = check_and_create_inventory_alerts()

        notifications = []

        if alert_results:
            # إشعار المخزون النافد
            if alert_results['out_of_stock'] > 0:
                notifications.append({
                    'id': 'out_of_stock',
                    'title': 'مخزون نافد',
                    'message': f"{alert_results['out_of_stock']} منتج نفد من المخزون",
                    'type': 'danger',
                    'icon': 'bi-x-circle',
                    'url': '/warehouses/alerts/?alert_type=out_of_stock',
                    'time': timezone.now().strftime('%H:%M'),
                    'priority': 'urgent'
                })

            # إشعار المخزون المنخفض
            if alert_results['low_stock'] > 0:
                notifications.append({
                    'id': 'low_stock',
                    'title': 'مخزون منخفض',
                    'message': f"{alert_results['low_stock']} منتج وصل للحد الأدنى",
                    'type': 'warning',
                    'icon': 'bi-exclamation-triangle',
                    'url': '/warehouses/alerts/?alert_type=low_stock',
                    'time': timezone.now().strftime('%H:%M'),
                    'priority': 'high'
                })

            # إشعار المخزون الزائد
            if alert_results['overstock'] > 0:
                notifications.append({
                    'id': 'overstock',
                    'title': 'مخزون زائد',
                    'message': f"{alert_results['overstock']} منتج تجاوز الحد الأقصى",
                    'type': 'info',
                    'icon': 'bi-arrow-up-circle',
                    'url': '/warehouses/alerts/?alert_type=overstock',
                    'time': timezone.now().strftime('%H:%M'),
                    'priority': 'normal'
                })

        return JsonResponse({
            'success': True,
            'notifications': notifications,
            'count': len(notifications)
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e),
            'notifications': [],
            'count': 0
        })


@login_required
def stock_reports(request):
    """تقارير المخزون المتقدمة"""
    # الحصول على المعاملات من URL
    report_type = request.GET.get('type', 'summary')
    warehouse_id = request.GET.get('warehouse')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')

    # إعداد التواريخ الافتراضية
    if not date_from:
        date_from = timezone.now().replace(day=1).date()  # بداية الشهر
    else:
        date_from = datetime.strptime(date_from, '%Y-%m-%d').date()

    if not date_to:
        date_to = timezone.now().date()  # اليوم
    else:
        date_to = datetime.strptime(date_to, '%Y-%m-%d').date()

    # الحصول على البيانات الأساسية
    warehouses = WarehouseDefinition.objects.filter(is_active=True)

    # تصفية المخازن حسب الاختيار
    if warehouse_id:
        selected_warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)
        inventory_items = InventoryItem.objects.filter(
            warehouse=selected_warehouse,
            is_active=True
        ).select_related('product', 'warehouse')
    else:
        selected_warehouse = None
        inventory_items = InventoryItem.objects.filter(
            is_active=True
        ).select_related('product', 'warehouse')

    # تقرير ملخص المخزون
    if report_type == 'summary':
        # إحصائيات عامة
        total_items = inventory_items.count()
        total_value = inventory_items.aggregate(
            total=Sum('total_value')
        )['total'] or 0

        # تصنيف المخزون حسب القيمة
        high_value_items = inventory_items.filter(total_value__gt=10000).count()
        medium_value_items = inventory_items.filter(
            total_value__gt=5000,
            total_value__lte=10000
        ).count()
        low_value_items = inventory_items.filter(total_value__lte=5000).count()

        # المخزون المنخفض والنافد
        low_stock_items = inventory_items.filter(
            quantity_on_hand__lte=F('minimum_stock')
        ).exclude(minimum_stock=0)

        out_of_stock_items = inventory_items.filter(quantity_on_hand=0)

        context = {
            'report_type': report_type,
            'warehouses': warehouses,
            'selected_warehouse': selected_warehouse,
            'date_from': date_from,
            'date_to': date_to,
            'total_items': total_items,
            'total_value': total_value,
            'high_value_items': high_value_items,
            'medium_value_items': medium_value_items,
            'low_value_items': low_value_items,
            'low_stock_items': low_stock_items,
            'out_of_stock_items': out_of_stock_items,
            'inventory_items': inventory_items[:20],  # أول 20 عنصر للعرض
        }

    # تقرير الحركات
    elif report_type == 'movements':
        # الحصول على الحركات في الفترة المحددة
        transactions = InventoryTransaction.objects.filter(
            created_at__date__range=[date_from, date_to]
        ).select_related('inventory_item__product', 'inventory_item__warehouse', 'created_by')

        if warehouse_id:
            transactions = transactions.filter(inventory_item__warehouse_id=warehouse_id)

        # إحصائيات الحركات
        total_transactions = transactions.count()
        total_receipts = transactions.filter(transaction_type='receipt').count()
        total_issues = transactions.filter(transaction_type='issue').count()
        total_transfers = transactions.filter(
            transaction_type__in=['transfer_in', 'transfer_out']
        ).count()
        total_adjustments = transactions.filter(transaction_type='count_adjustment').count()

        # قيم الحركات
        receipts_value = transactions.filter(transaction_type='receipt').aggregate(
            total=Sum('total_cost')
        )['total'] or 0

        issues_value = transactions.filter(transaction_type='issue').aggregate(
            total=Sum('total_cost')
        )['total'] or 0

        context = {
            'report_type': report_type,
            'warehouses': warehouses,
            'selected_warehouse': selected_warehouse,
            'date_from': date_from,
            'date_to': date_to,
            'transactions': transactions[:50],  # أول 50 حركة
            'total_transactions': total_transactions,
            'total_receipts': total_receipts,
            'total_issues': total_issues,
            'total_transfers': total_transfers,
            'total_adjustments': total_adjustments,
            'receipts_value': receipts_value,
            'issues_value': issues_value,
        }

    # تقرير ABC Analysis
    elif report_type == 'abc_analysis':
        # ترتيب العناصر حسب القيمة
        items_by_value = inventory_items.order_by('-total_value')
        total_value = items_by_value.aggregate(total=Sum('total_value'))['total'] or 0

        # تصنيف ABC
        a_items = []
        b_items = []
        c_items = []

        cumulative_value = 0
        for item in items_by_value:
            cumulative_value += item.total_value
            percentage = (cumulative_value / total_value * 100) if total_value > 0 else 0

            if percentage <= 80:
                a_items.append(item)
            elif percentage <= 95:
                b_items.append(item)
            else:
                c_items.append(item)

        context = {
            'report_type': report_type,
            'warehouses': warehouses,
            'selected_warehouse': selected_warehouse,
            'date_from': date_from,
            'date_to': date_to,
            'a_items': a_items,
            'b_items': b_items,
            'c_items': c_items,
            'total_value': total_value,
        }

    else:
        context = {
            'report_type': 'summary',
            'warehouses': warehouses,
            'selected_warehouse': selected_warehouse,
            'date_from': date_from,
            'date_to': date_to,
        }

    return render(request, 'warehouses/stock_reports.html', context)


@login_required
def low_stock_alerts(request):
    """تنبيهات المخزون المنخفض والنافد"""
    # الحصول على المعاملات من URL
    warehouse_id = request.GET.get('warehouse')
    alert_type = request.GET.get('type', 'all')

    # تصفية المخازن
    warehouses = WarehouseDefinition.objects.filter(is_active=True)

    # الحصول على عناصر المخزون
    inventory_items = InventoryItem.objects.filter(is_active=True).select_related(
        'product', 'warehouse'
    )

    if warehouse_id:
        selected_warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)
        inventory_items = inventory_items.filter(warehouse=selected_warehouse)
    else:
        selected_warehouse = None

    # تصفية حسب نوع التنبيه
    if alert_type == 'out_of_stock':
        # المخزون النافد
        alert_items = inventory_items.filter(quantity_on_hand=0)
        alert_title = "المخزون النافد"
        alert_icon = "x-circle"
        alert_color = "danger"
    elif alert_type == 'low_stock':
        # المخزون المنخفض
        alert_items = inventory_items.filter(
            quantity_on_hand__lte=F('minimum_stock'),
            quantity_on_hand__gt=0,
            minimum_stock__gt=0
        )
        alert_title = "المخزون المنخفض"
        alert_icon = "exclamation-triangle"
        alert_color = "warning"
    elif alert_type == 'overstock':
        # المخزون الزائد
        alert_items = inventory_items.filter(
            quantity_on_hand__gte=F('maximum_stock'),
            maximum_stock__gt=0
        )
        alert_title = "المخزون الزائد"
        alert_icon = "arrow-up-circle"
        alert_color = "info"
    else:
        # جميع التنبيهات
        from django.db.models import Q

        alert_items = inventory_items.filter(
            Q(quantity_on_hand=0) |  # نافد
            Q(quantity_on_hand__lte=F('minimum_stock'), quantity_on_hand__gt=0, minimum_stock__gt=0) |  # منخفض
            Q(quantity_on_hand__gte=F('maximum_stock'), maximum_stock__gt=0)  # زائد
        )
        alert_title = "جميع التنبيهات"
        alert_icon = "bell"
        alert_color = "primary"

    # إحصائيات التنبيهات
    total_alerts = alert_items.count()
    out_of_stock_count = inventory_items.filter(quantity_on_hand=0).count()
    low_stock_count = inventory_items.filter(
        quantity_on_hand__lte=F('minimum_stock'),
        quantity_on_hand__gt=0,
        minimum_stock__gt=0
    ).count()
    overstock_count = inventory_items.filter(
        quantity_on_hand__gte=F('maximum_stock'),
        maximum_stock__gt=0
    ).count()

    # حساب القيم المفقودة (بناءً على سعر التكلفة الحقيقي)
    out_of_stock_value = inventory_items.filter(quantity_on_hand=0).aggregate(
        total=Sum(F('minimum_stock') * F('product__cost_price'))
    )['total'] or 0

    low_stock_value = 0
    for item in inventory_items.filter(
        quantity_on_hand__lte=F('minimum_stock'),
        quantity_on_hand__gt=0,
        minimum_stock__gt=0
    ):
        shortage = item.minimum_stock - item.quantity_on_hand
        low_stock_value += shortage * item.product.cost_price

    context = {
        'warehouses': warehouses,
        'selected_warehouse': selected_warehouse,
        'alert_type': alert_type,
        'alert_title': alert_title,
        'alert_icon': alert_icon,
        'alert_color': alert_color,
        'alert_items': alert_items,
        'total_alerts': total_alerts,
        'out_of_stock_count': out_of_stock_count,
        'low_stock_count': low_stock_count,
        'overstock_count': overstock_count,
        'out_of_stock_value': out_of_stock_value,
        'low_stock_value': low_stock_value,
    }

    return render(request, 'warehouses/low_stock_alerts.html', context)


@login_required
def stock_adjustments(request):
    """تسويات المخزون"""
    if request.method == 'POST':
        try:
            warehouse_id = request.POST.get('warehouse')
            product_id = request.POST.get('product')
            adjustment_type = request.POST.get('adjustment_type')
            quantity = Decimal(request.POST.get('quantity', 0))
            unit_cost = Decimal(request.POST.get('unit_cost', 0))
            reason = request.POST.get('reason', '')
            notes = request.POST.get('notes', '')

            # التحقق من صحة البيانات
            if not warehouse_id or not product_id or not adjustment_type or quantity <= 0:
                messages.error(request, 'يرجى ملء جميع الحقول المطلوبة بشكل صحيح.')
                return redirect('warehouses:stock_adjustments')

            warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)
            product = get_object_or_404(ProductDefinition, id=product_id)

            # البحث عن أو إنشاء عنصر المخزون
            inventory_item, created = InventoryItem.objects.get_or_create(
                warehouse=warehouse,
                product=product,
                defaults={
                    'quantity_on_hand': Decimal('0'),
                    'average_cost': unit_cost or Decimal('0'),
                    'last_cost': unit_cost or Decimal('0'),
                    'minimum_stock': product.minimum_stock or Decimal('0'),
                    'maximum_stock': product.maximum_stock or Decimal('1000'),
                    'total_value': Decimal('0'),
                    'is_active': True
                }
            )

            # حفظ الرصيد قبل التسوية
            balance_before = inventory_item.quantity_on_hand
            old_total_value = inventory_item.total_value

            # تطبيق التسوية حسب النوع
            if adjustment_type == 'increase':
                # زيادة المخزون
                new_quantity = inventory_item.quantity_on_hand + quantity

                # حساب متوسط التكلفة الجديد
                if unit_cost > 0:
                    old_total_cost = inventory_item.quantity_on_hand * inventory_item.average_cost
                    new_total_cost = old_total_cost + (quantity * unit_cost)
                    if new_quantity > 0:
                        inventory_item.average_cost = new_total_cost / new_quantity
                    inventory_item.last_cost = unit_cost

                inventory_item.quantity_on_hand = new_quantity
                inventory_item.total_value = inventory_item.quantity_on_hand * inventory_item.average_cost
                inventory_item.last_received_date = timezone.now()

                transaction_type = 'adjustment_increase'
                transaction_number = f"ADJ-IN-{timezone.now().strftime('%Y%m%d%H%M%S')}"

            elif adjustment_type == 'decrease':
                # تقليل المخزون
                if quantity > inventory_item.quantity_on_hand:
                    messages.error(request, f'لا يمكن تقليل المخزون بأكثر من الكمية المتاحة ({inventory_item.quantity_on_hand}).')
                    return redirect('warehouses:stock_adjustments')

                inventory_item.quantity_on_hand -= quantity
                inventory_item.total_value = inventory_item.quantity_on_hand * inventory_item.average_cost
                inventory_item.last_issued_date = timezone.now()

                transaction_type = 'adjustment_decrease'
                transaction_number = f"ADJ-OUT-{timezone.now().strftime('%Y%m%d%H%M%S')}"

            elif adjustment_type in ['correction', 'set_quantity']:
                # تحديد كمية محددة
                inventory_item.quantity_on_hand = quantity

                # تحديث التكلفة إذا تم تحديدها
                if unit_cost > 0:
                    inventory_item.average_cost = unit_cost
                    inventory_item.last_cost = unit_cost

                inventory_item.total_value = inventory_item.quantity_on_hand * inventory_item.average_cost
                inventory_item.last_counted_date = timezone.now()

                transaction_type = 'adjustment_set'
                transaction_number = f"ADJ-SET-{timezone.now().strftime('%Y%m%d%H%M%S')}"

            elif adjustment_type in ['damage', 'expired', 'lost']:
                # أنواع تسوية تقليل المخزون
                if quantity > inventory_item.quantity_on_hand:
                    messages.error(request, f'لا يمكن تقليل المخزون بأكثر من الكمية المتاحة ({inventory_item.quantity_on_hand}).')
                    return redirect('warehouses:stock_adjustments')

                inventory_item.quantity_on_hand -= quantity
                inventory_item.total_value = inventory_item.quantity_on_hand * inventory_item.average_cost
                inventory_item.last_issued_date = timezone.now()

                transaction_type = f'adjustment_{adjustment_type}'
                transaction_number = f"ADJ-{adjustment_type.upper()}-{timezone.now().strftime('%Y%m%d%H%M%S')}"

            elif adjustment_type == 'found':
                # العثور على مخزون مفقود (زيادة)
                new_quantity = inventory_item.quantity_on_hand + quantity

                # حساب متوسط التكلفة الجديد
                if unit_cost > 0:
                    old_total_cost = inventory_item.quantity_on_hand * inventory_item.average_cost
                    new_total_cost = old_total_cost + (quantity * unit_cost)
                    if new_quantity > 0:
                        inventory_item.average_cost = new_total_cost / new_quantity
                    inventory_item.last_cost = unit_cost

                inventory_item.quantity_on_hand = new_quantity
                inventory_item.total_value = inventory_item.quantity_on_hand * inventory_item.average_cost
                inventory_item.last_received_date = timezone.now()

                transaction_type = 'adjustment_found'
                transaction_number = f"ADJ-FOUND-{timezone.now().strftime('%Y%m%d%H%M%S')}"

            elif adjustment_type == 'revalue':
                # إعادة تقييم (تغيير التكلفة فقط)
                if unit_cost <= 0:
                    messages.error(request, 'يجب تحديد تكلفة صحيحة لإعادة التقييم.')
                    return redirect('warehouses:stock_adjustments')

                inventory_item.average_cost = unit_cost
                inventory_item.last_cost = unit_cost
                inventory_item.total_value = inventory_item.quantity_on_hand * inventory_item.average_cost

                transaction_type = 'adjustment_revalue'
                transaction_number = f"ADJ-VAL-{timezone.now().strftime('%Y%m%d%H%M%S')}"
                quantity = Decimal('0')  # لا تغيير في الكمية

            # حفظ التغييرات
            inventory_item.save()

            # إنشاء حركة التسوية
            InventoryTransaction.objects.create(
                transaction_number=transaction_number,
                transaction_type=transaction_type,
                inventory_item=inventory_item,
                quantity=quantity,
                unit_cost=unit_cost or inventory_item.average_cost,
                total_cost=quantity * (unit_cost or inventory_item.average_cost),
                balance_before=balance_before,
                balance_after=inventory_item.quantity_on_hand,
                reference_number=transaction_number,
                notes=f"تسوية مخزون - {reason}. {notes}",
                created_by=request.user,
                is_approved=True,
                approved_by=request.user,
                approved_at=timezone.now()
            )

            # إنشاء سجل التسوية
            adjustment_date = request.POST.get('adjustment_date')
            if adjustment_date:
                try:
                    from datetime import datetime
                    adjustment_datetime = datetime.fromisoformat(adjustment_date.replace('T', ' '))
                except:
                    adjustment_datetime = timezone.now()
            else:
                adjustment_datetime = timezone.now()

            StockAdjustment.objects.create(
                adjustment_number=transaction_number,
                adjustment_date=adjustment_datetime,
                adjustment_type=adjustment_type,
                inventory_item=inventory_item,
                quantity=quantity if adjustment_type != 'revalue' else Decimal('0'),
                unit_cost=unit_cost or inventory_item.average_cost,
                total_value=quantity * (unit_cost or inventory_item.average_cost) if adjustment_type != 'revalue' else Decimal('0'),
                reason=reason,
                notes=notes,
                created_by=request.user,
                is_approved=True,
                approved_by=request.user,
                approved_at=timezone.now()
            )

            # رسالة النجاح
            if adjustment_type == 'increase':
                messages.success(request, f'تم زيادة مخزون {product.name} بمقدار {quantity} بنجاح!')
            elif adjustment_type == 'decrease':
                messages.success(request, f'تم تقليل مخزون {product.name} بمقدار {quantity} بنجاح!')
            elif adjustment_type in ['correction', 'set_quantity']:
                messages.success(request, f'تم تصحيح كمية {product.name} إلى {quantity} بنجاح!')
            elif adjustment_type == 'damage':
                messages.success(request, f'تم تسجيل تلف {quantity} من {product.name} بنجاح!')
            elif adjustment_type == 'expired':
                messages.success(request, f'تم تسجيل انتهاء صلاحية {quantity} من {product.name} بنجاح!')
            elif adjustment_type == 'lost':
                messages.success(request, f'تم تسجيل فقدان {quantity} من {product.name} بنجاح!')
            elif adjustment_type == 'found':
                messages.success(request, f'تم تسجيل العثور على {quantity} من {product.name} بنجاح!')
            elif adjustment_type == 'revalue':
                messages.success(request, f'تم إعادة تقييم {product.name} بتكلفة {unit_cost} ج.م بنجاح!')

            return redirect('warehouses:inventory_list')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تسوية المخزون: {str(e)}')

    # GET request
    warehouses = WarehouseDefinition.objects.filter(is_active=True)
    products = ProductDefinition.objects.filter(is_active=True)

    # إذا تم تحديد مخزن ومنتج، احصل على البيانات الحالية
    selected_warehouse = request.GET.get('warehouse')
    selected_product = request.GET.get('product')
    current_quantity = None
    current_cost = None
    current_value = None
    last_cost = None
    minimum_stock = None
    maximum_stock = None
    product_details = None
    warehouse_details = None

    if selected_warehouse and selected_product:
        # أولاً احصل على بيانات المنتج والمخزن
        try:
            product_details = ProductDefinition.objects.get(id=selected_product, is_active=True)
            warehouse_details = WarehouseDefinition.objects.get(id=selected_warehouse, is_active=True)

            # ثم ابحث عن عنصر المخزون
            try:
                inventory_item = InventoryItem.objects.get(
                    warehouse_id=selected_warehouse,
                    product_id=selected_product,
                    is_active=True
                )
                current_quantity = float(inventory_item.quantity_on_hand or 0)
                current_cost = float(inventory_item.average_cost or 0)
                current_value = float(inventory_item.total_value or 0)
                last_cost = float(inventory_item.last_cost or 0)
                minimum_stock = float(inventory_item.minimum_stock or 0)
                maximum_stock = float(inventory_item.maximum_stock or 1000)
            except InventoryItem.DoesNotExist:
                # إذا لم يكن المنتج موجود في المخزن، أنشئ بيانات افتراضية
                current_quantity = 0.0
                current_cost = 0.0
                current_value = 0.0
                last_cost = 0.0
                minimum_stock = 0.0
                maximum_stock = 1000.0

        except (ProductDefinition.DoesNotExist, WarehouseDefinition.DoesNotExist):
            # إذا لم يوجد المنتج أو المخزن
            product_details = None
            warehouse_details = None

    # أسباب التسوية الشائعة
    adjustment_reasons = [
        'فقدان أو تلف',
        'خطأ في الجرد',
        'إعادة تقييم',
        'تصحيح خطأ إدخال',
        'انتهاء صلاحية',
        'عينات أو هدايا',
        'سرقة أو اختلاس',
        'تسوية نهاية السنة',
        'أخرى'
    ]

    # إضافة التاريخ والوقت الحالي
    from datetime import datetime
    current_datetime = datetime.now().strftime('%Y-%m-%dT%H:%M')

    context = {
        'warehouses': warehouses,
        'products': products,
        'selected_warehouse': selected_warehouse,
        'selected_product': selected_product,
        'current_quantity': current_quantity,
        'current_cost': current_cost,
        'current_value': current_value,
        'last_cost': last_cost,
        'minimum_stock': minimum_stock,
        'maximum_stock': maximum_stock,
        'product_details': product_details,
        'warehouse_details': warehouse_details,
        'adjustment_reasons': adjustment_reasons,
        'current_datetime': current_datetime,
        'page_title': 'تسويات المخزون',
    }

    return render(request, 'warehouses/stock_adjustments.html', context)


@login_required
def adjustments_history(request):
    """قائمة سجل التسويات"""
    # الفلاتر
    warehouse_id = request.GET.get('warehouse')
    product_id = request.GET.get('product')
    adjustment_type = request.GET.get('adjustment_type')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    search_query = request.GET.get('search', '')

    # البحث والفلترة
    adjustments = StockAdjustment.objects.select_related(
        'inventory_item__warehouse', 'inventory_item__product', 'created_by'
    ).all().order_by('-created_at')

    if warehouse_id:
        adjustments = adjustments.filter(inventory_item__warehouse_id=warehouse_id)

    if product_id:
        adjustments = adjustments.filter(inventory_item__product_id=product_id)

    if adjustment_type:
        adjustments = adjustments.filter(adjustment_type=adjustment_type)

    if date_from:
        adjustments = adjustments.filter(adjustment_date__gte=date_from)

    if date_to:
        adjustments = adjustments.filter(adjustment_date__lte=date_to)

    if search_query:
        adjustments = adjustments.filter(
            Q(inventory_item__product__name__icontains=search_query) |
            Q(inventory_item__product__code__icontains=search_query) |
            Q(inventory_item__warehouse__name__icontains=search_query) |
            Q(reason__icontains=search_query) |
            Q(notes__icontains=search_query)
        )

    # البيانات للفلاتر
    warehouses = WarehouseDefinition.objects.filter(is_active=True)
    products = ProductDefinition.objects.filter(is_active=True)

    # أنواع التسوية (متطابقة مع النموذج)
    adjustment_types = [
        ('increase', 'زيادة'),
        ('decrease', 'نقص'),
        ('correction', 'تصحيح'),
        ('damage', 'تالف'),
        ('expired', 'منتهي الصلاحية'),
        ('lost', 'مفقود'),
        ('found', 'موجود'),
    ]

    # إحصائيات
    total_adjustments = adjustments.count()
    total_value_impact = adjustments.aggregate(
        total=Sum('total_value')
    )['total'] or 0

    context = {
        'adjustments': adjustments,
        'warehouses': warehouses,
        'products': products,
        'adjustment_types': adjustment_types,
        'selected_warehouse': warehouse_id,
        'selected_product': product_id,
        'selected_adjustment_type': adjustment_type,
        'date_from': date_from,
        'date_to': date_to,
        'search_query': search_query,
        'total_adjustments': total_adjustments,
        'total_value_impact': total_value_impact,
        'page_title': 'سجل تسويات المخزون',
    }

    return render(request, 'warehouses/adjustments_history.html', context)


@login_required
@require_http_methods(["GET"])
def get_stock_info(request):
    """الحصول على معلومات المخزون عبر AJAX"""
    warehouse_id = request.GET.get('warehouse_id')
    product_id = request.GET.get('product_id')

    if not warehouse_id or not product_id:
        return JsonResponse({'error': 'معرف المخزن والمنتج مطلوبان'}, status=400)

    try:
        warehouse = WarehouseDefinition.objects.get(id=warehouse_id, is_active=True)
        product = ProductDefinition.objects.get(id=product_id, is_active=True)

        # البحث عن عنصر المخزون
        try:
            inventory_item = InventoryItem.objects.get(
                warehouse=warehouse,
                product=product,
                is_active=True
            )
            current_quantity = float(inventory_item.quantity_on_hand)
        except InventoryItem.DoesNotExist:
            # إذا لم يوجد عنصر مخزون، فالكمية صفر
            current_quantity = 0.0

        # الحصول على أسعار المنتج من التعريفات (الأسعار الحقيقية)
        product_cost_price = float(product.cost_price) if product.cost_price else 0.0
        product_selling_price = float(product.selling_price) if product.selling_price else 0.0
        product_wholesale_price = float(product.wholesale_price) if product.wholesale_price else 0.0

        # حساب القيمة الحقيقية بناءً على سعر التكلفة الحقيقي
        current_value = current_quantity * product_cost_price

        return JsonResponse({
            'success': True,
            'warehouse_name': warehouse.name,
            'warehouse_code': warehouse.code,
            'product_name': product.name,
            'product_code': product.code,
            'current_quantity': current_quantity,
            'current_value': current_value,
            'product_cost_price': product_cost_price,
            'product_selling_price': product_selling_price,
            'product_wholesale_price': product_wholesale_price
        })

    except WarehouseDefinition.DoesNotExist:
        return JsonResponse({'error': 'المخزن غير موجود'}, status=404)
    except ProductDefinition.DoesNotExist:
        return JsonResponse({'error': 'المنتج غير موجود'}, status=404)
    except Exception as e:
        return JsonResponse({'error': f'خطأ في الخادم: {str(e)}'}, status=500)
