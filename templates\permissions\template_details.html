{% extends 'permissions/base.html' %}

{% block title %}تفاصيل القالب: {{ template.name }} - إدارة الصلاحيات{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'permissions:dashboard' %}">إدارة الصلاحيات</a></li>
        <li class="breadcrumb-item"><a href="{% url 'permissions:permission_templates' %}">قوالب الصلاحيات</a></li>
        <li class="breadcrumb-item active">{{ template.name }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="page-title">
                <i class="bi bi-file-text me-3"></i>
                {{ template.name }}
            </h1>
            <p class="page-subtitle">{{ template.description|default:"قالب صلاحيات" }}</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'permissions:permission_templates' %}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-right me-2"></i>
                العودة للقوالب
            </a>
            <a href="{% url 'permissions:edit_permission_template' template.id %}" class="btn btn-warning me-2">
                <i class="bi bi-pencil me-2"></i>
                تعديل القالب
            </a>
            <button class="btn btn-primary" onclick="applyTemplate({{ template.id }})">
                <i class="bi bi-person-plus me-2"></i>
                تطبيق على مستخدم
            </button>
        </div>
    </div>
</div>

<!-- معلومات أساسية وإحصائيات -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <i class="bi bi-info-circle me-2"></i>
                معلومات القالب
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>اسم القالب:</strong></td>
                                <td>{{ template.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>الوصف:</strong></td>
                                <td>{{ template.description|default:"لا يوجد وصف" }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الإنشاء:</strong></td>
                                <td>{{ template.created_at|date:"Y/m/d H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    {% if template.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>آخر تحديث:</strong></td>
                                <td>{{ template.updated_at|date:"Y/m/d H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="bi bi-bar-chart me-2"></i>
                إحصائيات سريعة
            </div>
            <div class="card-body">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">{{ total_permissions }}</div>
                        <div class="stat-label">إجمالي الصلاحيات</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ total_modules }}</div>
                        <div class="stat-label">عدد الوحدات</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ users_with_similar_permissions.count }}</div>
                        <div class="stat-label">مستخدمين مشابهين</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الصلاحيات المضمنة -->
<div class="card">
    <div class="card-header">
        <i class="bi bi-shield-check me-2"></i>
        الصلاحيات المضمنة في القالب
    </div>
    <div class="card-body">
        {% if permissions_by_module %}
            <div class="accordion" id="permissionsAccordion">
                {% for module, permissions in permissions_by_module.items %}
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" 
                                data-bs-toggle="collapse" 
                                data-bs-target="#collapse{{ forloop.counter }}"
                                aria-expanded="false">
                            <i class="bi bi-folder me-2"></i>
                            {{ module }} 
                            <span class="badge bg-primary ms-2">{{ permissions|length }}</span>
                        </button>
                    </h2>
                    <div id="collapse{{ forloop.counter }}" 
                         class="accordion-collapse collapse" 
                         data-bs-parent="#permissionsAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                {% for permission in permissions %}
                                <div class="col-md-6 mb-3">
                                    <div class="permission-item">
                                        <div class="d-flex align-items-start">
                                            <i class="bi bi-check-circle text-success me-2 mt-1"></i>
                                            <div>
                                                <strong>{{ permission.get_permission_type_display }}</strong>
                                                <br>
                                                <small class="text-muted">{{ permission.description|default:"لا يوجد وصف" }}</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                لا توجد صلاحيات مضافة لهذا القالب بعد.
                <a href="{% url 'permissions:permission_templates' %}" class="alert-link">العودة لتعديل القالب</a>
            </div>
        {% endif %}
    </div>
</div>

<!-- المستخدمين المشابهين -->
{% if users_with_similar_permissions %}
<div class="card mt-4">
    <div class="card-header">
        <i class="bi bi-people me-2"></i>
        مستخدمين لديهم صلاحيات مشابهة
    </div>
    <div class="card-body">
        <div class="row">
            {% for user_profile in users_with_similar_permissions %}
            <div class="col-md-3 mb-3">
                <div class="user-card">
                    <div class="d-flex align-items-center">
                        <div class="user-avatar">
                            {% if user_profile.avatar %}
                                <img src="{{ user_profile.avatar.url }}" alt="{{ user_profile.user.get_full_name }}">
                            {% else %}
                                <i class="bi bi-person-circle"></i>
                            {% endif %}
                        </div>
                        <div class="user-info">
                            <div class="user-name">{{ user_profile.user.get_full_name|default:user_profile.user.username }}</div>
                            <div class="user-position">{{ user_profile.job_position.name|default:"غير محدد" }}</div>
                        </div>
                    </div>
                    <div class="user-actions mt-2">
                        <a href="{% url 'permissions:user_permissions' user_profile.user.id %}" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-eye me-1"></i>
                            عرض الصلاحيات
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        {% if users_with_similar_permissions.count > 10 %}
        <div class="text-center mt-3">
            <button class="btn btn-outline-secondary" onclick="loadMoreUsers()">
                <i class="bi bi-plus-circle me-2"></i>
                عرض المزيد من المستخدمين
            </button>
        </div>
        {% endif %}
    </div>
</div>
{% endif %}

<!-- أزرار الإجراءات -->
<div class="text-center mt-4">
    <button class="btn btn-success btn-lg me-3" onclick="applyTemplate({{ template.id }})">
        <i class="bi bi-person-plus me-2"></i>
        تطبيق القالب على مستخدم
    </button>
    <a href="{% url 'permissions:permission_templates' %}" class="btn btn-outline-secondary btn-lg">
        <i class="bi bi-arrow-left me-2"></i>
        العودة للقوالب
    </a>
</div>
{% endblock %}

{% block extra_css %}
<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

.permission-item {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid var(--success-color);
    height: 100%;
}

.user-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
    height: 100%;
}

.user-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 1rem;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-avatar i {
    font-size: 1.8rem;
}

.user-name {
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

.user-position {
    font-size: 0.9rem;
    color: #6c757d;
}

.accordion-button {
    background: #f8f9fa;
    border: none;
}

.accordion-button:not(.collapsed) {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    color: var(--primary-color);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function applyTemplate(templateId) {
    const userId = prompt('أدخل رقم المستخدم لتطبيق القالب عليه:');
    if (userId) {
        fetch('{% url "permissions:apply_template_to_user" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: `template_id=${templateId}&user_id=${userId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}

function loadMoreUsers() {
    alert('سيتم إضافة وظيفة تحميل المزيد من المستخدمين قريباً');
}
</script>
{% endblock %}
