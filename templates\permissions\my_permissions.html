{% extends 'permissions/base.html' %}

{% block title %}صلاحياتي - نظام أوساريك{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'permissions:dashboard' %}">إدارة الصلاحيات</a></li>
        <li class="breadcrumb-item active">صلاحياتي</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <div class="d-flex align-items-center">
                <div class="user-avatar me-4">
                    {% if user_profile.avatar %}
                        <img src="{{ user_profile.avatar.url }}" alt="Avatar" class="rounded-circle" width="80" height="80">
                    {% else %}
                        <div class="avatar-circle-large">
                            <i class="bi bi-person"></i>
                        </div>
                    {% endif %}
                </div>
                <div>
                    <h1 class="page-title mb-2">صلاحياتي في النظام</h1>
                    <p class="page-subtitle mb-1">
                        <i class="bi bi-person me-2"></i>{{ user.get_full_name|default:user.username }}
                        {% if user.email %}
                            <span class="mx-2">•</span>
                            <i class="bi bi-envelope me-2"></i>{{ user.email }}
                        {% endif %}
                    </p>
                    <div class="d-flex gap-2 mt-2">
                        {% if user.is_superuser %}
                            <span class="badge bg-warning text-dark">
                                <i class="bi bi-star me-1"></i>مدير عام - صلاحيات كاملة
                            </span>
                        {% else %}
                            {% if user_profile.job_position %}
                                <span class="badge bg-info">
                                    <i class="bi bi-briefcase me-1"></i>{{ user_profile.job_position.name }}
                                </span>
                                <span class="badge bg-secondary">
                                    <i class="bi bi-building me-1"></i>{{ user_profile.job_position.department.name }}
                                </span>
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 text-end">
            <a href="/" class="btn btn-outline-primary">
                <i class="bi bi-house me-2"></i>
                العودة للرئيسية
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- User Info -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <i class="bi bi-info-circle me-2"></i>
                معلوماتي الشخصية
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>اسم المستخدم:</strong>
                    <span class="float-end">{{ user.username }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>الاسم الكامل:</strong>
                    <span class="float-end">{{ user.get_full_name|default:"غير محدد" }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>البريد الإلكتروني:</strong>
                    <span class="float-end">{{ user.email|default:"غير محدد" }}</span>
                </div>
                
                {% if user_profile %}
                    {% if user_profile.job_position %}
                        <div class="mb-3">
                            <strong>المنصب الوظيفي:</strong>
                            <span class="float-end">{{ user_profile.job_position.name }}</span>
                        </div>
                        
                        <div class="mb-3">
                            <strong>القسم:</strong>
                            <span class="float-end">{{ user_profile.job_position.department.name }}</span>
                        </div>
                    {% endif %}
                    
                    {% if user_profile.phone %}
                        <div class="mb-3">
                            <strong>رقم الهاتف:</strong>
                            <span class="float-end">{{ user_profile.phone }}</span>
                        </div>
                    {% endif %}
                {% endif %}
                
                <div class="mb-3">
                    <strong>تاريخ التسجيل:</strong>
                    <span class="float-end">{{ user.date_joined|date:"Y-m-d" }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>آخر دخول:</strong>
                    <span class="float-end">
                        {% if user.last_login %}
                            {{ user.last_login|date:"Y-m-d H:i" }}
                        {% else %}
                            لم يسجل دخول
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>
        
        <!-- Permissions Summary -->
        <div class="card">
            <div class="card-header">
                <i class="bi bi-bar-chart me-2"></i>
                ملخص الصلاحيات
            </div>
            <div class="card-body">
                {% if user.is_superuser %}
                    <div class="text-center py-4">
                        <i class="bi bi-star fs-1 text-warning"></i>
                        <h5 class="text-warning mt-3">مدير عام</h5>
                        <p class="text-muted">لديك صلاحيات كاملة في جميع أجزاء النظام</p>
                    </div>
                {% else %}
                    {% if user_profile %}
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-number">{{ user_profile.permissions.count }}</div>
                                    <div class="stat-label">إجمالي الصلاحيات</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-number">{{ permissions_by_module|length }}</div>
                                    <div class="stat-label">الوحدات المتاحة</div>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="permissions-breakdown">
                            <h6 class="text-muted mb-3">توزيع الصلاحيات:</h6>
                            {% for module, perms in permissions_by_module.items %}
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="small">{{ module }}</span>
                                    <span class="badge bg-primary">{{ perms|length }}</span>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-exclamation-triangle fs-1 text-warning"></i>
                            <h6 class="text-warning mt-3">لم يتم تعيين صلاحيات</h6>
                            <p class="text-muted small">يرجى التواصل مع المدير لتعيين الصلاحيات</p>
                        </div>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Permissions Details -->
    <div class="col-lg-8">
        {% if user.is_superuser %}
            <div class="card">
                <div class="card-header">
                    <i class="bi bi-shield-check me-2"></i>
                    صلاحيات المدير العام
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-star me-2"></i>
                        <strong>مدير عام:</strong> لديك صلاحيات كاملة للوصول إلى جميع أجزاء النظام وإدارتها.
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="permission-card">
                                <i class="bi bi-speedometer2 text-primary"></i>
                                <h6>لوحة التحكم</h6>
                                <p class="small text-muted">وصول كامل لجميع الإحصائيات والتقارير</p>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="permission-card">
                                <i class="bi bi-people text-success"></i>
                                <h6>إدارة المستخدمين</h6>
                                <p class="small text-muted">إضافة وتعديل وحذف المستخدمين والصلاحيات</p>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="permission-card">
                                <i class="bi bi-gear text-info"></i>
                                <h6>إعدادات النظام</h6>
                                <p class="small text-muted">تكوين وإدارة جميع إعدادات النظام</p>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="permission-card">
                                <i class="bi bi-shield-check text-warning"></i>
                                <h6>إدارة الصلاحيات</h6>
                                <p class="small text-muted">إنشاء وتعديل صلاحيات جميع المستخدمين</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% else %}
            {% if permissions_by_module %}
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-shield-check me-2"></i>
                        صلاحياتي التفصيلية
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="permissionsAccordion">
                            {% for module, permissions in permissions_by_module.items %}
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading{{ forloop.counter }}">
                                    <button class="accordion-button {% if not forloop.first %}collapsed{% endif %}" 
                                            type="button" 
                                            data-bs-toggle="collapse" 
                                            data-bs-target="#collapse{{ forloop.counter }}"
                                            aria-expanded="{% if forloop.first %}true{% else %}false{% endif %}"
                                            aria-controls="collapse{{ forloop.counter }}">
                                        <i class="bi bi-folder me-2"></i>
                                        <strong>{{ module }}</strong>
                                        <span class="badge bg-primary ms-auto me-3">{{ permissions|length }} صلاحية</span>
                                    </button>
                                </h2>
                                <div id="collapse{{ forloop.counter }}" 
                                     class="accordion-collapse collapse {% if forloop.first %}show{% endif %}"
                                     aria-labelledby="heading{{ forloop.counter }}"
                                     data-bs-parent="#permissionsAccordion">
                                    <div class="accordion-body">
                                        <div class="row">
                                            {% for permission in permissions %}
                                            <div class="col-md-6 col-lg-4 mb-3">
                                                <div class="permission-item-readonly">
                                                    <i class="bi bi-check-circle text-success me-2"></i>
                                                    <strong>{{ permission }}</strong>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-shield-x fs-1 text-muted"></i>
                        <h5 class="text-muted mt-3">لا توجد صلاحيات مخصصة</h5>
                        <p class="text-muted">لم يتم تخصيص أي صلاحيات لحسابك حتى الآن</p>
                        <p class="text-muted small">يرجى التواصل مع مدير النظام لتخصيص الصلاحيات المناسبة</p>
                    </div>
                </div>
            {% endif %}
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar-circle-large {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
    }
    
    .user-avatar img {
        object-fit: cover;
    }
    
    .stat-item {
        text-align: center;
        padding: 1rem 0;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
    }
    
    .stat-label {
        font-size: 0.9rem;
        color: #6c757d;
        font-weight: 600;
    }
    
    .permission-card {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }
    
    .permission-card:hover {
        background: white;
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
    }
    
    .permission-card i {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    
    .permission-card h6 {
        color: var(--dark-color);
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .permission-item-readonly {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 0.75rem;
        border-left: 4px solid var(--success-color);
    }
    
    .permissions-breakdown {
        max-height: 200px;
        overflow-y: auto;
    }
    
    .accordion-button:not(.collapsed) {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        color: var(--primary-color);
    }
</style>
{% endblock %}
