{% extends 'permissions/base.html' %}

{% block title %}إنشاء قالب صلاحيات جديد - إدارة الصلاحيات{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'permissions:dashboard' %}">إدارة الصلاحيات</a></li>
        <li class="breadcrumb-item"><a href="{% url 'permissions:permission_templates' %}">قوالب الصلاحيات</a></li>
        <li class="breadcrumb-item active">إنشاء قالب جديد</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="page-title">
                <i class="bi bi-plus-circle me-3"></i>
                إنشاء قالب صلاحيات جديد
            </h1>
            <p class="page-subtitle">إنشاء قالب صلاحيات مخصص للمناصب الوظيفية</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'permissions:permission_templates' %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-right me-2"></i>
                العودة للقوالب
            </a>
        </div>
    </div>
</div>

<form method="post" id="templateForm">
    {% csrf_token %}
    
    <div class="row">
        <!-- Template Info -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات القالب
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">اسم القالب *</label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="text-danger small">{{ form.name.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">المنصب الوظيفي *</label>
                        {{ form.job_position }}
                        {% if form.job_position.errors %}
                            <div class="text-danger small">{{ form.job_position.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">وصف القالب</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger small">{{ form.description.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                القالب نشط
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Presets -->
            <div class="card">
                <div class="card-header">
                    <i class="bi bi-lightning me-2"></i>
                    إعدادات سريعة
                </div>
                <div class="card-body">
                    <p class="text-muted small mb-3">اختر نمط صلاحيات جاهز</p>
                    
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="applyPreset('admin')">
                            <i class="bi bi-star me-2"></i>مدير عام (كل الصلاحيات)
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="applyPreset('manager')">
                            <i class="bi bi-person-badge me-2"></i>مدير قسم (عرض + إدارة)
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="applyPreset('employee')">
                            <i class="bi bi-person me-2"></i>موظف (عرض + إضافة)
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="applyPreset('viewer')">
                            <i class="bi bi-eye me-2"></i>مشاهد (عرض فقط)
                        </button>
                    </div>
                    
                    <hr>
                    
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="selectAllPermissions()">
                            <i class="bi bi-check-all me-2"></i>تحديد الكل
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearAllPermissions()">
                            <i class="bi bi-x-square me-2"></i>إلغاء الكل
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Permissions Selection -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>
                        <i class="bi bi-shield-check me-2"></i>
                        اختيار الصلاحيات
                    </span>
                    <span class="badge bg-light text-dark" id="selectedCount">0 صلاحية محددة</span>
                </div>
                
                <div class="card-body">
                    <!-- Permissions by Module -->
                    <div class="accordion" id="permissionsAccordion">
                        {% for module, permissions in permissions_by_module.items %}
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading{{ forloop.counter }}">
                                <button class="accordion-button {% if not forloop.first %}collapsed{% endif %}" 
                                        type="button" 
                                        data-bs-toggle="collapse" 
                                        data-bs-target="#collapse{{ forloop.counter }}"
                                        aria-expanded="{% if forloop.first %}true{% else %}false{% endif %}"
                                        aria-controls="collapse{{ forloop.counter }}">
                                    <i class="bi bi-folder me-2"></i>
                                    <strong>{{ permissions.0.get_module_display }}</strong>
                                    <span class="badge bg-primary ms-auto me-3">{{ permissions|length }} صلاحية</span>
                                </button>
                            </h2>
                            <div id="collapse{{ forloop.counter }}" 
                                 class="accordion-collapse collapse {% if forloop.first %}show{% endif %}"
                                 aria-labelledby="heading{{ forloop.counter }}"
                                 data-bs-parent="#permissionsAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        {% for permission in permissions %}
                                        <div class="col-md-6 col-lg-4 mb-3">
                                            <div class="form-check permission-item">
                                                <input class="form-check-input permission-checkbox" 
                                                       type="checkbox" 
                                                       name="permissions" 
                                                       value="{{ permission.id }}"
                                                       id="permission_{{ permission.id }}"
                                                       data-module="{{ module }}">
                                                <label class="form-check-label" for="permission_{{ permission.id }}">
                                                    <strong>{{ permission.get_permission_type_display }}</strong>
                                                    {% if permission.description %}
                                                        <br><small class="text-muted">{{ permission.description }}</small>
                                                    {% endif %}
                                                </label>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    
                                    <!-- Module Quick Actions -->
                                    <div class="mt-3 pt-3 border-top">
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-primary me-2" 
                                                onclick="selectModulePermissions('{{ module }}')">
                                            <i class="bi bi-check-square me-1"></i>تحديد الكل
                                        </button>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-secondary" 
                                                onclick="clearModulePermissions('{{ module }}')">
                                            <i class="bi bi-square me-1"></i>إلغاء الكل
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Submit Button -->
    <div class="text-center mt-4">
        <button type="submit" class="btn btn-success btn-lg">
            <i class="bi bi-check-circle me-2"></i>
            إنشاء القالب
        </button>
        <a href="{% url 'permissions:permission_templates' %}" class="btn btn-outline-secondary btn-lg ms-3">
            <i class="bi bi-x-circle me-2"></i>
            إلغاء
        </a>
    </div>
</form>
{% endblock %}

{% block extra_css %}
<style>
    .permission-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }
    
    .permission-item:hover {
        background: #e9ecef;
        transform: translateY(-2px);
    }
    
    .permission-item .form-check-input:checked + .form-check-label {
        color: var(--primary-color);
        font-weight: 600;
    }
    
    .accordion-button:not(.collapsed) {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        color: var(--primary-color);
    }
    
    #selectedCount {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
function updateSelectedCount() {
    const checkedBoxes = document.querySelectorAll('.permission-checkbox:checked');
    const count = checkedBoxes.length;
    document.getElementById('selectedCount').textContent = `${count} صلاحية محددة`;
}

function selectAllPermissions() {
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedCount();
}

function clearAllPermissions() {
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedCount();
}

function selectModulePermissions(module) {
    document.querySelectorAll(`.permission-checkbox[data-module="${module}"]`).forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedCount();
}

function clearModulePermissions(module) {
    document.querySelectorAll(`.permission-checkbox[data-module="${module}"]`).forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedCount();
}

function applyPreset(presetType) {
    clearAllPermissions();
    
    const modules = [
        {% for module, permissions in permissions_by_module.items %}
        '{{ module }}'{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];
    
    switch(presetType) {
        case 'admin':
            selectAllPermissions();
            break;
            
        case 'manager':
            modules.forEach(module => {
                ['view', 'add', 'edit', 'approve', 'manage'].forEach(permType => {
                    const checkbox = document.querySelector(`input[data-module="${module}"][value*="${permType}"]`);
                    if (checkbox) checkbox.checked = true;
                });
            });
            break;
            
        case 'employee':
            modules.forEach(module => {
                ['view', 'add', 'edit'].forEach(permType => {
                    const checkbox = document.querySelector(`input[data-module="${module}"][value*="${permType}"]`);
                    if (checkbox) checkbox.checked = true;
                });
            });
            break;
            
        case 'viewer':
            modules.forEach(module => {
                const checkbox = document.querySelector(`input[data-module="${module}"][value*="view"]`);
                if (checkbox) checkbox.checked = true;
            });
            break;
    }
    
    updateSelectedCount();
}

// Add event listeners to all checkboxes
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });
    
    updateSelectedCount();
});

// Form validation
document.getElementById('templateForm').addEventListener('submit', function(e) {
    const checkedBoxes = document.querySelectorAll('.permission-checkbox:checked');
    
    if (checkedBoxes.length === 0) {
        e.preventDefault();
        alert('يجب اختيار صلاحية واحدة على الأقل');
        return false;
    }
    
    const templateName = document.querySelector('input[name="name"]').value.trim();
    if (!templateName) {
        e.preventDefault();
        alert('يجب إدخال اسم القالب');
        return false;
    }
    
    return true;
});
</script>
{% endblock %}
