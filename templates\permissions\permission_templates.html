{% extends 'permissions/base.html' %}

{% block title %}قوالب الصلاحيات - إدارة الصلاحيات{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'permissions:dashboard' %}">إدارة الصلاحيات</a></li>
        <li class="breadcrumb-item active">قوالب الصلاحيات</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="page-title">
                <i class="bi bi-file-earmark-text me-3"></i>
                قوالب الصلاحيات
            </h1>
            <p class="page-subtitle">إدارة قوالب الصلاحيات الجاهزة للمناصب المختلفة</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'permissions:create_permission_template' %}" class="btn btn-success">
                <i class="bi bi-plus-circle me-2"></i>
                إنشاء قالب جديد
            </a>
        </div>
    </div>
</div>

<!-- Templates Grid -->
<div class="row">
    {% for template in templates %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card template-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0">{{ template.name }}</h5>
                    <small class="text-light">{{ template.job_position.department.name }}</small>
                </div>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-three-dots-vertical"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="#" onclick="editTemplate({{ template.id }})">
                                <i class="bi bi-pencil me-2"></i>تعديل
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" onclick="duplicateTemplate({{ template.id }})">
                                <i class="bi bi-files me-2"></i>نسخ
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="#" onclick="deleteTemplate({{ template.id }})">
                                <i class="bi bi-trash me-2"></i>حذف
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="card-body">
                <div class="mb-3">
                    <span class="badge bg-info mb-2">
                        <i class="bi bi-briefcase me-1"></i>
                        {{ template.job_position.name }}
                    </span>
                    {% if template.is_active %}
                        <span class="badge badge-success mb-2">
                            <i class="bi bi-check-circle me-1"></i>نشط
                        </span>
                    {% else %}
                        <span class="badge badge-danger mb-2">
                            <i class="bi bi-x-circle me-1"></i>غير نشط
                        </span>
                    {% endif %}
                </div>
                
                <p class="text-muted small">{{ template.description|default:"لا يوجد وصف" }}</p>
                
                <div class="permissions-summary">
                    <h6 class="text-muted mb-2">الصلاحيات المتضمنة:</h6>
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="stat-item">
                                <div class="stat-number">{{ template.permissions.count }}</div>
                                <div class="stat-label">إجمالي</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <div class="stat-number">{{ template.modules_count }}</div>
                                <div class="stat-label">وحدات</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <div class="stat-number">{{ template.created_at|date:"m-d" }}</div>
                                <div class="stat-label">تاريخ الإنشاء</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card-footer">
                <div class="d-flex gap-2">
                    <button class="btn btn-primary btn-sm flex-fill" onclick="showTemplateDetails({{ template.id }})">
                        <i class="bi bi-eye me-1"></i>عرض التفاصيل
                    </button>
                    <button class="btn btn-success btn-sm flex-fill" onclick="applyToUser({{ template.id }})">
                        <i class="bi bi-person-plus me-1"></i>تطبيق على مستخدم
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="bi bi-file-earmark-text fs-1 text-muted"></i>
            <h5 class="text-muted mt-3">لا توجد قوالب صلاحيات</h5>
            <p class="text-muted">ابدأ بإنشاء قالب صلاحيات جديد</p>
            <a href="{% url 'permissions:create_permission_template' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>
                إنشاء قالب جديد
            </a>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Template Details Modal -->
<div class="modal fade" id="templateDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل القالب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="templateDetailsContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Apply to User Modal -->
<div class="modal fade" id="applyToUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تطبيق القالب على مستخدم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="applyTemplateForm">
                    <input type="hidden" id="selectedTemplateId" name="template_id">
                    
                    <div class="mb-3">
                        <label class="form-label">اختر المستخدم</label>
                        <select class="form-select" name="user_id" required>
                            <option value="">-- اختر المستخدم --</option>
                            <!-- Users will be loaded here -->
                        </select>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>تنبيه:</strong> سيتم استبدال جميع الصلاحيات الحالية للمستخدم بصلاحيات هذا القالب.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="confirmApplyTemplate()">
                    <i class="bi bi-check me-2"></i>تطبيق القالب
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .template-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    
    .template-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .template-card .card-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
    }
    
    .permissions-summary {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .stat-item {
        text-align: center;
    }
    
    .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
    }
    
    .stat-label {
        font-size: 0.8rem;
        color: #6c757d;
        font-weight: 600;
    }
    
    .card-footer {
        background: #f8f9fa;
        border-top: 1px solid #dee2e6;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
function showTemplateDetails(templateId) {
    // Load template details via AJAX
    fetch(`/permissions/templates/${templateId}/details/`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        document.getElementById('templateDetailsContent').innerHTML = html;
        new bootstrap.Modal(document.getElementById('templateDetailsModal')).show();
    })
    .catch(error => {
        console.error('Error loading template details:', error);
        alert('حدث خطأ في تحميل التفاصيل');
    });
}

function applyToUser(templateId) {
    // تطبيق القالب على مستخدم - مبسط
    const userId = prompt('أدخل رقم المستخدم لتطبيق القالب عليه:');
    if (userId) {
        fetch('{% url "permissions:apply_template_to_user" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: `template_id=${templateId}&user_id=${userId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}

function confirmApplyTemplate() {
    const form = document.getElementById('applyTemplateForm');
    const formData = new FormData(form);
    
    fetch('{% url "permissions:apply_template_to_user" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            bootstrap.Modal.getInstance(document.getElementById('applyToUserModal')).hide();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        alert('حدث خطأ في الاتصال');
    });
}

function editTemplate(templateId) {
    window.location.href = `/permissions/templates/${templateId}/edit/`;
}

function duplicateTemplate(templateId) {
    alert('سيتم إضافة وظيفة نسخ القالب قريباً');
}

function deleteTemplate(templateId) {
    if (confirm('هل أنت متأكد من حذف هذا القالب؟ لا يمكن التراجع عن هذا الإجراء.')) {
        alert('سيتم إضافة وظيفة حذف القالب قريباً');
    }
}
</script>
{% endblock %}
